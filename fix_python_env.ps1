# Python环境诊断与修复脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Python环境诊断与修复工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Python安装
Write-Host "[1/6] 检查Python安装状态..." -ForegroundColor Yellow
Write-Host ""

try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Python已安装并在PATH中" -ForegroundColor Green
        Write-Host $pythonVersion -ForegroundColor Green
        $pythonWorking = $true
    } else {
        Write-Host "✗ Python未找到或无法执行" -ForegroundColor Red
        $pythonWorking = $false
    }
} catch {
    Write-Host "✗ Python命令执行失败" -ForegroundColor Red
    $pythonWorking = $false
}

Write-Host ""

# 搜索Python安装
Write-Host "[2/6] 搜索已安装的Python..." -ForegroundColor Yellow
$pythonPaths = @(
    "$env:LOCALAPPDATA\Programs\Python",
    "C:\Program Files\Python*",
    "C:\Python*",
    "$env:LOCALAPPDATA\Microsoft\WindowsApps\python.exe"
)

$foundPython = $false
foreach ($path in $pythonPaths) {
    if (Test-Path $path) {
        Write-Host "✓ 发现Python安装: $path" -ForegroundColor Green
        $foundPython = $true
    }
}

# 特别检查Windows Store Python
if (Test-Path "$env:LOCALAPPDATA\Microsoft\WindowsApps\python.exe") {
    Write-Host "⚠ 发现Windows Store Python (可能有问题)" -ForegroundColor Yellow
    Write-Host "  建议卸载并安装官方版本" -ForegroundColor Yellow
}

if (-not $foundPython -and -not $pythonWorking) {
    Write-Host "✗ 未发现可用的Python安装" -ForegroundColor Red
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "需要安装Python" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "请按照以下步骤安装Python:" -ForegroundColor White
    Write-Host ""
    Write-Host "方案1 - 官方Python:" -ForegroundColor Cyan
    Write-Host "1. 访问 https://www.python.org/downloads/" -ForegroundColor White
    Write-Host "2. 下载Python 3.10或更高版本" -ForegroundColor White
    Write-Host "3. 运行安装程序时：" -ForegroundColor White
    Write-Host "   ✓ 勾选 'Add Python to PATH'" -ForegroundColor Green
    Write-Host "   ✓ 勾选 'Install for all users'" -ForegroundColor Green
    Write-Host "   ✓ 选择 'Customize installation'" -ForegroundColor Green
    Write-Host "   ✓ 确保包含 pip" -ForegroundColor Green
    Write-Host ""
    Write-Host "方案2 - Anaconda:" -ForegroundColor Cyan
    Write-Host "1. 访问 https://www.anaconda.com/products/distribution" -ForegroundColor White
    Write-Host "2. 下载并安装Anaconda" -ForegroundColor White
    Write-Host "3. 打开Anaconda Prompt" -ForegroundColor White
    Write-Host "4. 运行: conda create -n management python=3.10" -ForegroundColor White
    Write-Host "5. 运行: conda activate management" -ForegroundColor White
    Write-Host ""
    Write-Host "安装完成后请重新运行此脚本" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host ""

# 检查pip
Write-Host "[3/6] 检查pip状态..." -ForegroundColor Yellow
try {
    $pipVersion = pip --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ pip可用" -ForegroundColor Green
        Write-Host $pipVersion -ForegroundColor Green
    } else {
        Write-Host "✗ pip不可用，尝试修复..." -ForegroundColor Red
        python -m ensurepip --upgrade
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ pip修复成功" -ForegroundColor Green
        } else {
            Write-Host "✗ pip修复失败" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "✗ pip检查失败" -ForegroundColor Red
}

Write-Host ""

# 检查必需包
Write-Host "[4/6] 检查必需的包..." -ForegroundColor Yellow
$packages = @("streamlit", "pandas", "plotly")
foreach ($package in $packages) {
    try {
        $result = pip show $package 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ $package 已安装" -ForegroundColor Green
        } else {
            Write-Host "✗ $package 未安装，正在安装..." -ForegroundColor Red
            pip install $package
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ $package 安装成功" -ForegroundColor Green
            } else {
                Write-Host "✗ $package 安装失败" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "✗ $package 检查失败" -ForegroundColor Red
    }
}

Write-Host ""

# 测试Python环境
Write-Host "[5/6] 测试Python环境..." -ForegroundColor Yellow
try {
    $testResult = python -c "import sys; print('Python版本:', sys.version); print('Python路径:', sys.executable)" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Python环境测试通过" -ForegroundColor Green
        Write-Host $testResult -ForegroundColor Cyan
    } else {
        Write-Host "✗ Python环境测试失败" -ForegroundColor Red
        Write-Host $testResult -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Python环境测试异常" -ForegroundColor Red
}

Write-Host ""

# 测试包导入
Write-Host "[6/6] 测试必需包导入..." -ForegroundColor Yellow
try {
    $importResult = python -c "import streamlit, pandas, plotly; print('所有包导入成功')" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 所有必需包可正常导入" -ForegroundColor Green
        Write-Host $importResult -ForegroundColor Green
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "🎉 Python环境配置完成！" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "下一步：运行以下命令启动系统" -ForegroundColor Yellow
        Write-Host "PowerShell: .\start_system.ps1" -ForegroundColor Cyan
        Write-Host "或者: python run.py" -ForegroundColor Cyan
    } else {
        Write-Host "✗ 包导入失败，正在重新安装..." -ForegroundColor Red
        pip install --upgrade streamlit pandas plotly
        Write-Host "请重新运行此脚本验证安装" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ 包导入测试异常" -ForegroundColor Red
}

Write-Host ""
Read-Host "按Enter键退出"
