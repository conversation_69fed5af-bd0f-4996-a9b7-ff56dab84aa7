# 综合管理系统部署指南

## 📋 系统概述

综合管理系统是一个基于Python + Streamlit + SQLite的企业级工作日志与项目管理平台，集成了韵通网络的文档管理模式和iOS风格的用户界面设计。

## 🛠️ 技术栈

- **后端**: Python 3.10+, Streamlit, SQLite
- **前端**: HTML5, Tailwind CSS, JavaScript
- **数据库**: SQLite
- **开发工具**: VS Code, Git

## 📦 环境要求

### 系统要求
- Python 3.10 或更高版本
- 操作系统: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- 内存: 最少 2GB RAM
- 存储: 最少 1GB 可用空间

### Python依赖
```bash
streamlit>=1.28.0
pandas>=2.0.0
plotly>=5.15.0
fastapi>=0.100.0
uvicorn>=0.23.0
bcrypt>=4.0.0
python-jose>=3.3.0
python-multipart>=0.0.6
pytest>=7.4.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0
```

## 🚀 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd comprehensive-management-system
```

### 2. 安装依赖
```bash
pip install -r config/requirements.txt
```

### 3. 初始化数据库
```bash
python backend/utils/init_db.py
```

### 4. 启动系统
```bash
# 方式一：使用启动脚本（推荐）
python run.py

# 方式二：手动启动
# 启动后端
streamlit run backend/app.py --server.port=8501

# 启动前端（新终端）
cd frontend
python -m http.server 8080
```

### 5. 访问系统
- **前端用户界面**: http://localhost:8080
- **后端管理界面**: http://localhost:8501

## 👤 默认账号

| 用户类型 | 用户名 | 密码 | 权限 |
|---------|--------|------|------|
| 管理员 | admin | admin123 | 全部权限 |
| 测试用户 | yangjun | 123456 | 普通用户 |

## 📁 项目结构

```
comprehensive-management-system/
├── backend/                 # 后端代码
│   ├── models/             # 数据模型
│   ├── services/           # 业务服务
│   ├── utils/              # 工具函数
│   └── app.py              # Streamlit主应用
├── frontend/               # 前端代码
│   ├── css/                # 样式文件
│   ├── js/                 # JavaScript文件
│   └── index.html          # 主页面
├── config/                 # 配置文件
├── database/               # 数据库文件
├── docs/                   # 文档
├── tests/                  # 测试文件
└── run.py                  # 启动脚本
```

## ⚙️ 配置说明

### 数据库配置
- 数据库文件: `database/comprehensive_management.db`
- 自动创建表结构和初始数据
- 支持数据备份和恢复

### 服务端口配置
- Streamlit后端: 8501端口
- 前端服务器: 8080端口
- 可在启动脚本中修改端口配置

### 日志配置
- 应用日志存储在 `logs/` 目录
- 支持按日期轮转
- 可配置日志级别

## 🔧 高级配置

### 1. 生产环境部署

#### 使用Nginx反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /api/ {
        proxy_pass http://localhost:8501/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 使用systemd服务
```ini
[Unit]
Description=Comprehensive Management System
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/project
ExecStart=/usr/bin/python3 run.py
Restart=always

[Install]
WantedBy=multi-user.target
```

### 2. 数据库备份
```bash
# 备份数据库
cp database/comprehensive_management.db backup/backup_$(date +%Y%m%d_%H%M%S).db

# 恢复数据库
cp backup/backup_20231201_120000.db database/comprehensive_management.db
```

### 3. 性能优化
- 启用SQLite WAL模式
- 配置适当的连接池大小
- 使用CDN加速静态资源

## 🧪 测试

### 运行测试
```bash
# 运行系统测试
python test_system.py

# 运行单元测试
pytest tests/

# 代码质量检查
black backend/ frontend/
flake8 backend/
mypy backend/
```

### 测试覆盖率
```bash
pytest --cov=backend tests/
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -tulpn | grep :8501
   
   # 杀死进程
   kill -9 <PID>
   ```

2. **数据库锁定**
   ```bash
   # 检查数据库连接
   sqlite3 database/comprehensive_management.db ".timeout 30000"
   ```

3. **依赖安装失败**
   ```bash
   # 升级pip
   pip install --upgrade pip
   
   # 使用国内镜像
   pip install -r config/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

### 日志查看
```bash
# 查看Streamlit日志
tail -f ~/.streamlit/logs/streamlit.log

# 查看应用日志
tail -f logs/app.log
```

## 📈 监控和维护

### 系统监控
- 监控CPU和内存使用率
- 监控数据库大小和性能
- 监控用户访问量和响应时间

### 定期维护
- 定期备份数据库
- 清理过期日志文件
- 更新系统依赖

## 🔒 安全配置

### 1. 密码策略
- 强制使用复杂密码
- 定期更换默认密码
- 启用密码过期策略

### 2. 访问控制
- 配置防火墙规则
- 限制管理员访问IP
- 启用HTTPS加密

### 3. 数据保护
- 定期备份重要数据
- 加密敏感信息
- 审计用户操作

## 📞 技术支持

如遇到问题，请按以下步骤处理：

1. 查看本文档的故障排除部分
2. 检查系统日志文件
3. 运行系统测试脚本
4. 联系技术支持团队

---

**版本**: v1.0.0  
**更新日期**: 2024年7月8日  
**维护团队**: 综合管理系统开发组
