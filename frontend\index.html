<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>综合管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/ios-style.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'ios-blue': '#007AFF',
                        'ios-gray': '#F2F2F7',
                        'ios-dark': '#1C1C1E',
                        'ios-light': '#FFFFFF'
                    },
                    fontFamily: {
                        'sf': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-ios-gray font-sf">
    <!-- 主容器 -->
    <div id="app" class="min-h-screen">
        <!-- 登录页面 -->
        <div id="loginPage" class="min-h-screen flex items-center justify-center p-4">
            <div class="w-full max-w-md">
                <!-- Logo区域 -->
                <div class="text-center mb-8">
                    <div class="w-20 h-20 bg-ios-blue rounded-2xl mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-2xl"></i>
                    </div>
                    <h1 class="text-2xl font-bold text-ios-dark mb-2">综合管理系统</h1>
                    <p class="text-gray-600">企业级工作日志与项目管理平台</p>
                </div>

                <!-- 登录表单 -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <form id="loginForm" class="p-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                                <input type="text" id="username" name="username" required
                                    class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ios-blue focus:border-transparent transition-all duration-200"
                                    placeholder="请输入用户名">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                <input type="password" id="password" name="password" required
                                    class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ios-blue focus:border-transparent transition-all duration-200"
                                    placeholder="请输入密码">
                            </div>
                        </div>
                        
                        <button type="submit" 
                            class="w-full mt-6 bg-ios-blue text-white py-3 rounded-xl font-medium hover:bg-blue-600 transition-colors duration-200 active:scale-95 transform">
                            登录
                        </button>
                    </form>
                    
                    <!-- 分割线 -->
                    <div class="px-6 py-4 border-t border-gray-100">
                        <div class="flex items-center justify-between text-sm">
                            <button id="registerBtn" class="text-ios-blue hover:underline">注册新账号</button>
                            <button id="initDbBtn" class="text-gray-500 hover:text-gray-700">初始化数据库</button>
                        </div>
                    </div>
                </div>

                <!-- 提示信息 -->
                <div id="loginMessage" class="mt-4 p-4 rounded-xl hidden">
                    <p class="text-sm text-center"></p>
                </div>
            </div>
        </div>

        <!-- 注册页面 -->
        <div id="registerPage" class="min-h-screen flex items-center justify-center p-4 hidden">
            <div class="w-full max-w-md">
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <div class="p-6">
                        <div class="flex items-center mb-6">
                            <button id="backToLogin" class="text-ios-blue mr-3">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                            <h2 class="text-xl font-bold text-ios-dark">注册新账号</h2>
                        </div>

                        <form id="registerForm">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                                    <input type="text" id="regUsername" name="username" required
                                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ios-blue focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                                    <input type="email" id="regEmail" name="email" required
                                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ios-blue focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">姓名</label>
                                    <input type="text" id="regFullName" name="fullName" required
                                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ios-blue focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">部门</label>
                                    <select id="regDepartment" name="department"
                                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ios-blue focus:border-transparent">
                                        <option value="">请选择部门</option>
                                        <option value="技术部">技术部</option>
                                        <option value="产品部">产品部</option>
                                        <option value="运营部">运营部</option>
                                        <option value="市场部">市场部</option>
                                        <option value="财务部">财务部</option>
                                        <option value="人事部">人事部</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">职位</label>
                                    <input type="text" id="regPosition" name="position"
                                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ios-blue focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                    <input type="password" id="regPassword" name="password" required
                                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ios-blue focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">确认密码</label>
                                    <input type="password" id="regConfirmPassword" name="confirmPassword" required
                                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ios-blue focus:border-transparent">
                                </div>
                            </div>

                            <!-- 用户协议 -->
                            <div class="mt-6">
                                <label class="flex items-start">
                                    <input type="checkbox" id="agreeTerms" required
                                        class="mt-1 mr-3 w-4 h-4 text-ios-blue border-gray-300 rounded focus:ring-ios-blue">
                                    <span class="text-sm text-gray-600">
                                        我已阅读并同意 <a href="#" class="text-ios-blue hover:underline">用户协议</a> 和 <a href="#" class="text-ios-blue hover:underline">隐私政策</a>
                                    </span>
                                </label>
                            </div>

                            <button type="submit" 
                                class="w-full mt-6 bg-ios-blue text-white py-3 rounded-xl font-medium hover:bg-blue-600 transition-colors duration-200 active:scale-95 transform">
                                注册
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 注册提示信息 -->
                <div id="registerMessage" class="mt-4 p-4 rounded-xl hidden">
                    <p class="text-sm text-center"></p>
                </div>
            </div>
        </div>

        <!-- 主应用页面 -->
        <div id="mainApp" class="hidden">
            <!-- 这里将通过JavaScript动态加载Streamlit应用 -->
            <div class="text-center p-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-ios-blue mx-auto mb-4"></div>
                <p class="text-gray-600">正在加载应用...</p>
            </div>
        </div>
    </div>

    <!-- Toast通知 -->
    <div id="toast" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white border border-gray-200 rounded-xl shadow-lg p-4 max-w-sm">
            <div class="flex items-center">
                <div id="toastIcon" class="mr-3"></div>
                <div>
                    <p id="toastTitle" class="font-medium text-gray-900"></p>
                    <p id="toastMessage" class="text-sm text-gray-600"></p>
                </div>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
