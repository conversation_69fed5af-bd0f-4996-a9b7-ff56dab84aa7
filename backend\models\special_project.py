"""
特殊项目模型
"""

from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from .base import BaseModel

class SpecialProject(BaseModel):
    """特殊项目模型"""
    
    table_name = "special_projects"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.id: Optional[int] = kwargs.get('id')
        self.month: str = kwargs.get('month', '')  # YYYY-MM格式
        self.project_name: str = kwargs.get('project_name', '')
        self.operator: Optional[str] = kwargs.get('operator')  # 运营商
        self.bandwidth_mbps: Optional[int] = kwargs.get('bandwidth_mbps')  # 带宽（M）
        self.payment_method: Optional[str] = kwargs.get('payment_method')  # 缴费方式
        self.received_amount: Optional[Decimal] = kwargs.get('received_amount')  # 收款金额
        self.business_fee: Optional[Decimal] = kwargs.get('business_fee')  # 商务费用
        self.actual_received: Optional[Decimal] = kwargs.get('actual_received')  # 实收
        self.business_fee_ratio: Optional[Decimal] = kwargs.get('business_fee_ratio')  # 商务费占比
        self.created_by: Optional[int] = kwargs.get('created_by')
        self.created_at: Optional[datetime] = kwargs.get('created_at')
        self.updated_at: Optional[datetime] = kwargs.get('updated_at')
        
        # 处理Decimal类型转换
        if isinstance(self.received_amount, (int, float, str)):
            self.received_amount = Decimal(str(self.received_amount)) if self.received_amount else None
        if isinstance(self.business_fee, (int, float, str)):
            self.business_fee = Decimal(str(self.business_fee)) if self.business_fee else None
        if isinstance(self.actual_received, (int, float, str)):
            self.actual_received = Decimal(str(self.actual_received)) if self.actual_received else None
        if isinstance(self.business_fee_ratio, (int, float, str)):
            self.business_fee_ratio = Decimal(str(self.business_fee_ratio)) if self.business_fee_ratio else None
    
    @classmethod
    def find_by_month(cls, month: str) -> List['SpecialProject']:
        """根据月份查找项目"""
        return cls.find_all("month = ? ORDER BY created_at DESC", (month,))
    
    @classmethod
    def find_by_operator(cls, operator: str) -> List['SpecialProject']:
        """根据运营商查找项目"""
        return cls.find_all("operator = ? ORDER BY month DESC", (operator,))
    
    @classmethod
    def get_monthly_summary(cls, month: str) -> dict:
        """获取月度汇总数据"""
        projects = cls.find_by_month(month)
        
        if not projects:
            return {
                'total_projects': 0,
                'total_received': Decimal('0'),
                'total_business_fee': Decimal('0'),
                'total_actual_received': Decimal('0'),
                'avg_business_fee_ratio': Decimal('0')
            }
        
        total_received = sum(p.received_amount or Decimal('0') for p in projects)
        total_business_fee = sum(p.business_fee or Decimal('0') for p in projects)
        total_actual_received = sum(p.actual_received or Decimal('0') for p in projects)
        
        # 计算平均商务费占比
        valid_ratios = [p.business_fee_ratio for p in projects if p.business_fee_ratio is not None]
        avg_ratio = sum(valid_ratios) / len(valid_ratios) if valid_ratios else Decimal('0')
        
        return {
            'total_projects': len(projects),
            'total_received': total_received,
            'total_business_fee': total_business_fee,
            'total_actual_received': total_actual_received,
            'avg_business_fee_ratio': avg_ratio
        }
    
    @classmethod
    def get_operator_stats(cls) -> List[dict]:
        """获取运营商统计"""
        db = cls._get_db_instance()
        query = """
            SELECT operator, 
                   COUNT(*) as project_count,
                   SUM(received_amount) as total_received,
                   SUM(business_fee) as total_business_fee,
                   AVG(business_fee_ratio) as avg_ratio
            FROM special_projects 
            WHERE operator IS NOT NULL
            GROUP BY operator
            ORDER BY total_received DESC
        """
        
        rows = db.execute_query(query)
        return [dict(row) for row in rows]
    
    def calculate_business_fee_ratio(self):
        """计算商务费占比"""
        if self.received_amount and self.business_fee and self.received_amount > 0:
            self.business_fee_ratio = (self.business_fee / self.received_amount) * 100
        else:
            self.business_fee_ratio = Decimal('0')
    
    def calculate_actual_received(self):
        """计算实收金额"""
        if self.received_amount and self.business_fee:
            self.actual_received = self.received_amount - self.business_fee
        else:
            self.actual_received = self.received_amount or Decimal('0')
    
    def get_creator(self):
        """获取创建者信息"""
        if self.created_by:
            from .user import User
            return User.find_by_id(self.created_by)
        return None
    
    def _insert(self) -> bool:
        """插入新项目"""
        # 自动计算相关字段
        self.calculate_business_fee_ratio()
        self.calculate_actual_received()
        
        query = """
            INSERT INTO special_projects (month, project_name, operator, bandwidth_mbps, 
                                        payment_method, received_amount, business_fee, 
                                        actual_received, business_fee_ratio, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            self.month, self.project_name, self.operator, self.bandwidth_mbps,
            self.payment_method, self.received_amount, self.business_fee,
            self.actual_received, self.business_fee_ratio, self.created_by
        )
        
        try:
            self.id = self.db.execute_insert(query, params)
            return True
        except Exception as e:
            print(f"插入特殊项目失败: {e}")
            return False
    
    def _update(self) -> bool:
        """更新项目"""
        # 自动计算相关字段
        self.calculate_business_fee_ratio()
        self.calculate_actual_received()
        
        query = """
            UPDATE special_projects 
            SET month=?, project_name=?, operator=?, bandwidth_mbps=?, payment_method=?,
                received_amount=?, business_fee=?, actual_received=?, business_fee_ratio=?,
                updated_at=CURRENT_TIMESTAMP
            WHERE id=?
        """
        params = (
            self.month, self.project_name, self.operator, self.bandwidth_mbps,
            self.payment_method, self.received_amount, self.business_fee,
            self.actual_received, self.business_fee_ratio, self.id
        )
        
        try:
            rowcount = self.db.execute_update(query, params)
            return rowcount > 0
        except Exception as e:
            print(f"更新特殊项目失败: {e}")
            return False
    
    @classmethod
    def _get_db_instance(cls):
        """获取数据库实例"""
        from .base import DatabaseManager
        return DatabaseManager()
    
    def to_dict(self):
        """转换为字典，处理Decimal类型"""
        result = super().to_dict()
        
        # 转换Decimal为float以便JSON序列化
        decimal_fields = ['received_amount', 'business_fee', 'actual_received', 'business_fee_ratio']
        for field in decimal_fields:
            if hasattr(self, field) and getattr(self, field) is not None:
                result[field] = float(getattr(self, field))
        
        return result
