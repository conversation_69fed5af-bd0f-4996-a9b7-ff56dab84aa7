# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# 默认所有文件
[*]
charset = utf-8                    # 文件编码统一为 UTF-8
end_of_line = lf                   # 换行符统一为 LF
insert_final_newline = true        # 文件末尾自动补一个空行
trim_trailing_whitespace = true    # 移除行尾多余空格
indent_style = space              # 统一缩进风格（space/tab）
indent_size = 2                   # 缩进宽度

# Python 文件
[*.py]
indent_size = 4
max_line_length = 88

# JavaScript 文件
[*.js]
indent_size = 2

# HTML 文件
[*.html]
indent_size = 2

# CSS 文件
[*.css]
indent_size = 2

# JSON 文件
[*.json]
indent_size = 2

# YAML 文件
[*.{yml,yaml}]
indent_size = 2

# Markdown 文件
[*.md]
trim_trailing_whitespace = false

# Makefile
[Makefile]
indent_style = tab
