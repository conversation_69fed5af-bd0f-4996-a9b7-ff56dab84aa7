"""
工作日志模型
"""

from typing import Optional, List
from datetime import datetime, date
from .base import BaseModel

class WorkLog(BaseModel):
    """工作日志模型"""
    
    table_name = "work_logs"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.id: Optional[int] = kwargs.get('id')
        self.user_id: int = kwargs.get('user_id', 0)
        self.log_date: Optional[date] = kwargs.get('log_date')
        self.content: str = kwargs.get('content', '')
        self.category: Optional[str] = kwargs.get('category')
        self.status: str = kwargs.get('status', 'submitted')
        self.created_at: Optional[datetime] = kwargs.get('created_at')
        self.updated_at: Optional[datetime] = kwargs.get('updated_at')
        
        # 处理日期字符串转换
        if isinstance(self.log_date, str):
            self.log_date = datetime.strptime(self.log_date, '%Y-%m-%d').date()
    
    @classmethod
    def find_by_user_and_date(cls, user_id: int, log_date: date) -> Optional['WorkLog']:
        """根据用户ID和日期查找日志"""
        logs = cls.find_all("user_id = ? AND log_date = ?", (user_id, log_date))
        return logs[0] if logs else None
    
    @classmethod
    def find_by_user(cls, user_id: int, limit: int = None) -> List['WorkLog']:
        """根据用户ID查找日志"""
        query_suffix = f" ORDER BY log_date DESC"
        if limit:
            query_suffix += f" LIMIT {limit}"
        
        return cls.find_all(f"user_id = ?{query_suffix}", (user_id,))
    
    @classmethod
    def find_by_date_range(cls, start_date: date, end_date: date, user_id: int = None) -> List['WorkLog']:
        """根据日期范围查找日志"""
        if user_id:
            where_clause = "log_date BETWEEN ? AND ? AND user_id = ?"
            params = (start_date, end_date, user_id)
        else:
            where_clause = "log_date BETWEEN ? AND ?"
            params = (start_date, end_date)
        
        return cls.find_all(f"{where_clause} ORDER BY log_date DESC", params)
    
    @classmethod
    def get_missing_logs(cls, user_id: int, start_date: date, end_date: date) -> List[date]:
        """获取指定日期范围内缺失的日志日期"""
        existing_logs = cls.find_by_date_range(start_date, end_date, user_id)
        existing_dates = {log.log_date for log in existing_logs}
        
        missing_dates = []
        current_date = start_date
        
        while current_date <= end_date:
            # 只检查工作日（周一到周五）
            if current_date.weekday() < 5 and current_date not in existing_dates:
                missing_dates.append(current_date)
            current_date = date.fromordinal(current_date.toordinal() + 1)
        
        return missing_dates
    
    def get_user(self):
        """获取日志对应的用户"""
        from .user import User
        return User.find_by_id(self.user_id)
    
    def _insert(self) -> bool:
        """插入新日志"""
        query = """
            INSERT INTO work_logs (user_id, log_date, content, category, status)
            VALUES (?, ?, ?, ?, ?)
        """
        params = (self.user_id, self.log_date, self.content, self.category, self.status)
        
        try:
            self.id = self.db.execute_insert(query, params)
            return True
        except Exception as e:
            print(f"插入工作日志失败: {e}")
            return False
    
    def _update(self) -> bool:
        """更新日志"""
        query = """
            UPDATE work_logs 
            SET content=?, category=?, status=?, updated_at=CURRENT_TIMESTAMP
            WHERE id=?
        """
        params = (self.content, self.category, self.status, self.id)
        
        try:
            rowcount = self.db.execute_update(query, params)
            return rowcount > 0
        except Exception as e:
            print(f"更新工作日志失败: {e}")
            return False
    
    def to_dict(self):
        """转换为字典，处理日期格式"""
        result = super().to_dict()
        if self.log_date:
            result['log_date'] = self.log_date.strftime('%Y-%m-%d')
        return result
