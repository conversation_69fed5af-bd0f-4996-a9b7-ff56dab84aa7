<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>综合管理系统 - 完整演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .ios-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .ios-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .ios-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .hidden { display: none; }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 via-white to-purple-50 min-h-screen">
    
    <!-- 登录页面 -->
    <div id="loginPage" class="min-h-screen flex items-center justify-center p-4">
        <div class="ios-card rounded-3xl p-8 w-full max-w-md">
            <div class="text-center mb-8">
                <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-chart-line text-3xl text-white"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800">综合管理系统</h1>
                <p class="text-gray-600 mt-2">企业级工作管理平台</p>
            </div>

            <form id="loginForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                    <input type="text" id="username" required
                           class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                    <input type="password" id="password" required
                           class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <button type="submit" class="w-full ios-button text-white py-3 px-4 rounded-xl font-medium">
                    <i class="fas fa-sign-in-alt mr-2"></i>登录
                </button>
            </form>

            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">演示账号：</p>
                <p class="text-xs text-gray-500">管理员: admin / admin123</p>
                <p class="text-xs text-gray-500">测试用户: yangjun / 123456</p>
            </div>

            <div id="loginMessage" class="hidden mt-4 p-3 rounded-xl">
                <p class="text-sm"></p>
            </div>
        </div>
    </div>

    <!-- 主应用界面 -->
    <div id="mainApp" class="hidden min-h-screen bg-gray-50">
        <!-- 内容将通过JavaScript动态加载 -->
    </div>

    <!-- Toast通知 -->
    <div id="toast" class="hidden fixed top-4 right-4 bg-white rounded-xl shadow-lg p-4 z-50 max-w-sm">
        <div class="flex items-start">
            <i id="toastIcon" class="fas fa-info-circle text-blue-500 mt-1"></i>
            <div class="ml-3">
                <p id="toastTitle" class="font-medium text-gray-800"></p>
                <p id="toastMessage" class="text-sm text-gray-600 mt-1"></p>
            </div>
        </div>
    </div>

    <script>
        class DemoApp {
            constructor() {
                this.currentUser = null;
                this.init();
            }

            init() {
                this.bindEvents();
                this.checkAuthStatus();
            }

            bindEvents() {
                document.getElementById('loginForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });
            }

            async handleLogin() {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                if (!username || !password) {
                    this.showMessage('请输入用户名和密码', 'error');
                    return;
                }

                // 模拟登录验证
                if ((username === 'admin' && password === 'admin123') ||
                    (username === 'yangjun' && password === '123456')) {
                    
                    this.currentUser = { 
                        username, 
                        fullName: username === 'admin' ? '管理员' : '杨俊',
                        role: username === 'admin' ? 'admin' : 'user'
                    };
                    
                    localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
                    this.showMessage('登录成功！正在跳转...', 'success');
                    
                    setTimeout(() => {
                        this.showMainApp();
                    }, 1000);
                } else {
                    this.showMessage('用户名或密码错误', 'error');
                }
            }

            showMainApp() {
                document.getElementById('loginPage').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
                this.loadMainInterface();
            }

            loadMainInterface() {
                const mainApp = document.getElementById('mainApp');
                mainApp.innerHTML = `
                    <div class="min-h-screen">
                        <!-- 顶部导航 -->
                        <nav class="bg-white shadow-sm border-b">
                            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                                <div class="flex justify-between h-16">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-chart-line text-white text-sm"></i>
                                        </div>
                                        <h1 class="ml-3 text-xl font-semibold text-gray-900">综合管理系统</h1>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <span class="text-sm text-gray-600">欢迎，${this.currentUser.fullName}</span>
                                        <button onclick="app.logout()" class="bg-red-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-600 transition-colors">
                                            <i class="fas fa-sign-out-alt mr-1"></i>退出
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </nav>

                        <!-- 主要内容 -->
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                            <!-- 统计卡片 -->
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                                <div class="bg-white rounded-xl shadow-sm p-6">
                                    <div class="flex items-center">
                                        <div class="p-3 rounded-full bg-blue-100">
                                            <i class="fas fa-users text-blue-600 text-xl"></i>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-medium text-gray-600">总用户数</p>
                                            <p class="text-2xl font-semibold text-gray-900">156</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-6">
                                    <div class="flex items-center">
                                        <div class="p-3 rounded-full bg-green-100">
                                            <i class="fas fa-clipboard-list text-green-600 text-xl"></i>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-medium text-gray-600">今日日志</p>
                                            <p class="text-2xl font-semibold text-gray-900">42</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-6">
                                    <div class="flex items-center">
                                        <div class="p-3 rounded-full bg-purple-100">
                                            <i class="fas fa-project-diagram text-purple-600 text-xl"></i>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-medium text-gray-600">本月项目</p>
                                            <p class="text-2xl font-semibold text-gray-900">18</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-6">
                                    <div class="flex items-center">
                                        <div class="p-3 rounded-full bg-orange-100">
                                            <i class="fas fa-chart-line text-orange-600 text-xl"></i>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-medium text-gray-600">完成率</p>
                                            <p class="text-2xl font-semibold text-gray-900">87%</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 功能模块 -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                                <div class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow">
                                    <div class="flex items-center mb-4">
                                        <div class="p-2 bg-blue-100 rounded-lg">
                                            <i class="fas fa-clipboard-list text-blue-600"></i>
                                        </div>
                                        <h3 class="ml-3 text-lg font-semibold text-gray-800">工作日志管理</h3>
                                    </div>
                                    <p class="text-gray-600 mb-4">记录和管理日常工作内容，追踪完成情况</p>
                                    <button onclick="app.showToast('工作日志', '演示功能：工作日志管理模块')" 
                                            class="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors">
                                        进入模块
                                    </button>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow">
                                    <div class="flex items-center mb-4">
                                        <div class="p-2 bg-green-100 rounded-lg">
                                            <i class="fas fa-project-diagram text-green-600"></i>
                                        </div>
                                        <h3 class="ml-3 text-lg font-semibold text-gray-800">特殊项目管理</h3>
                                    </div>
                                    <p class="text-gray-600 mb-4">管理特殊项目信息和财务数据</p>
                                    <button onclick="app.showToast('项目管理', '演示功能：特殊项目管理模块')" 
                                            class="w-full bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition-colors">
                                        进入模块
                                    </button>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow">
                                    <div class="flex items-center mb-4">
                                        <div class="p-2 bg-purple-100 rounded-lg">
                                            <i class="fas fa-chart-bar text-purple-600"></i>
                                        </div>
                                        <h3 class="ml-3 text-lg font-semibold text-gray-800">统计分析</h3>
                                    </div>
                                    <p class="text-gray-600 mb-4">查看各类统计报表和数据分析</p>
                                    <button onclick="app.showToast('统计分析', '演示功能：统计分析模块')" 
                                            class="w-full bg-purple-500 text-white py-2 px-4 rounded-lg hover:bg-purple-600 transition-colors">
                                        进入模块
                                    </button>
                                </div>
                            </div>

                            <!-- 系统状态 -->
                            <div class="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-6">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-yellow-600 text-xl mt-1"></i>
                                    <div class="ml-4">
                                        <h3 class="text-lg font-medium text-yellow-800">演示模式说明</h3>
                                        <div class="mt-2 text-sm text-yellow-700">
                                            <p class="mb-2">🎯 <strong>当前状态</strong>：前端演示版本，展示系统界面和基本交互</p>
                                            <p class="mb-2">🔧 <strong>完整功能</strong>：需要配置Python环境并启动后端服务</p>
                                            <p class="mb-2">📚 <strong>部署指南</strong>：请查看 docs/DEPLOYMENT.md 文件</p>
                                            <p>💡 <strong>技术栈</strong>：Python + Streamlit + SQLite + HTML5 + Tailwind CSS</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            checkAuthStatus() {
                const savedUser = localStorage.getItem('currentUser');
                if (savedUser) {
                    this.currentUser = JSON.parse(savedUser);
                    this.showMainApp();
                }
            }

            logout() {
                this.currentUser = null;
                localStorage.removeItem('currentUser');
                document.getElementById('mainApp').classList.add('hidden');
                document.getElementById('loginPage').classList.remove('hidden');
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
                this.showMessage('已退出登录', 'info');
            }

            showMessage(message, type) {
                const messageDiv = document.getElementById('loginMessage');
                const messageText = messageDiv.querySelector('p');
                
                messageDiv.classList.remove('hidden', 'bg-red-100', 'bg-green-100', 'bg-blue-100', 'text-red-700', 'text-green-700', 'text-blue-700');
                
                switch(type) {
                    case 'error':
                        messageDiv.classList.add('bg-red-100', 'text-red-700');
                        break;
                    case 'success':
                        messageDiv.classList.add('bg-green-100', 'text-green-700');
                        break;
                    case 'info':
                        messageDiv.classList.add('bg-blue-100', 'text-blue-700');
                        break;
                }
                
                messageText.textContent = message;
                messageDiv.classList.remove('hidden');
                
                if (type !== 'success') {
                    setTimeout(() => {
                        messageDiv.classList.add('hidden');
                    }, 3000);
                }
            }

            showToast(title, message) {
                const toast = document.getElementById('toast');
                const toastTitle = document.getElementById('toastTitle');
                const toastMessage = document.getElementById('toastMessage');

                toastTitle.textContent = title;
                toastMessage.textContent = message;

                toast.classList.remove('hidden');
                toast.classList.add('fade-in');

                setTimeout(() => {
                    toast.classList.add('hidden');
                    toast.classList.remove('fade-in');
                }, 3000);
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            window.app = new DemoApp();
        });
    </script>
</body>
</html>
