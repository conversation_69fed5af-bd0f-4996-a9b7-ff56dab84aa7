"""
审计日志模型
"""

from typing import Optional, Dict, Any
from datetime import datetime
import json
from .base import BaseModel

class AuditLog(BaseModel):
    """审计日志模型"""
    
    table_name = "audit_logs"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.id: Optional[int] = kwargs.get('id')
        self.user_id: Optional[int] = kwargs.get('user_id')
        self.action: str = kwargs.get('action', '')
        self.resource_type: Optional[str] = kwargs.get('resource_type')
        self.resource_id: Optional[int] = kwargs.get('resource_id')
        self.details: Optional[Dict[str, Any]] = kwargs.get('details')
        self.ip_address: Optional[str] = kwargs.get('ip_address')
        self.user_agent: Optional[str] = kwargs.get('user_agent')
        self.created_at: Optional[datetime] = kwargs.get('created_at')
        
        # 处理details字段的JSON转换
        if isinstance(self.details, str):
            try:
                self.details = json.loads(self.details)
            except (json.JSONDecodeError, TypeError):
                self.details = None
    
    def _insert(self) -> bool:
        """插入新审计日志"""
        details_json = json.dumps(self.details, ensure_ascii=False) if self.details else None
        
        query = """
            INSERT INTO audit_logs (user_id, action, resource_type, resource_id, 
                                  details, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            self.user_id, self.action, self.resource_type, self.resource_id,
            details_json, self.ip_address, self.user_agent
        )
        
        try:
            self.id = self.db.execute_insert(query, params)
            return True
        except Exception as e:
            print(f"插入审计日志失败: {e}")
            return False
    
    def _update(self) -> bool:
        """审计日志不允许更新"""
        return False
