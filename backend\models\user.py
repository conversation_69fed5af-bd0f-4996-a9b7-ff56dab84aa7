"""
用户模型
"""

from typing import Optional, List
from datetime import datetime, timedelta
import hashlib
import secrets
from .base import BaseModel, DatabaseManager

class User(BaseModel):
    """用户模型"""
    
    table_name = "users"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.id: Optional[int] = kwargs.get('id')
        self.username: str = kwargs.get('username', '')
        self.email: str = kwargs.get('email', '')
        self.password_hash: str = kwargs.get('password_hash', '')
        self.full_name: str = kwargs.get('full_name', '')
        self.department: Optional[str] = kwargs.get('department')
        self.position: Optional[str] = kwargs.get('position')
        self.phone: Optional[str] = kwargs.get('phone')
        self.is_active: bool = kwargs.get('is_active', True)
        self.is_admin: bool = kwargs.get('is_admin', False)
        self.created_at: Optional[datetime] = kwargs.get('created_at')
        self.updated_at: Optional[datetime] = kwargs.get('updated_at')
    
    @classmethod
    def find_by_username(cls, username: str) -> Optional['User']:
        """根据用户名查找用户"""
        users = cls.find_all("username = ?", (username,))
        return users[0] if users else None
    
    @classmethod
    def find_by_email(cls, email: str) -> Optional['User']:
        """根据邮箱查找用户"""
        users = cls.find_all("email = ?", (email,))
        return users[0] if users else None
    
    @staticmethod
    def hash_password(password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return self.password_hash == self.hash_password(password)
    
    def set_password(self, password: str):
        """设置密码"""
        self.password_hash = self.hash_password(password)
    
    def _insert(self) -> bool:
        """插入新用户"""
        query = """
            INSERT INTO users (username, email, password_hash, full_name, department, 
                             position, phone, is_active, is_admin)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            self.username, self.email, self.password_hash, self.full_name,
            self.department, self.position, self.phone, self.is_active, self.is_admin
        )
        
        try:
            self.id = self.db.execute_insert(query, params)
            return True
        except Exception as e:
            print(f"插入用户失败: {e}")
            return False
    
    def _update(self) -> bool:
        """更新用户信息"""
        query = """
            UPDATE users 
            SET username=?, email=?, full_name=?, department=?, position=?, 
                phone=?, is_active=?, is_admin=?, updated_at=CURRENT_TIMESTAMP
            WHERE id=?
        """
        params = (
            self.username, self.email, self.full_name, self.department,
            self.position, self.phone, self.is_active, self.is_admin, self.id
        )
        
        try:
            rowcount = self.db.execute_update(query, params)
            return rowcount > 0
        except Exception as e:
            print(f"更新用户失败: {e}")
            return False
    
    def create_session(self) -> Optional['UserSession']:
        """创建用户会话"""
        if not self.id:
            return None
        
        session = UserSession()
        session.user_id = self.id
        session.session_token = secrets.token_urlsafe(32)
        session.expires_at = datetime.now() + timedelta(days=7)  # 7天过期
        
        if session.save():
            return session
        return None
    
    def get_active_sessions(self) -> List['UserSession']:
        """获取用户的活跃会话"""
        if not self.id:
            return []
        
        return UserSession.find_all(
            "user_id = ? AND expires_at > CURRENT_TIMESTAMP",
            (self.id,)
        )

class UserSession(BaseModel):
    """用户会话模型"""
    
    table_name = "user_sessions"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.id: Optional[int] = kwargs.get('id')
        self.user_id: int = kwargs.get('user_id', 0)
        self.session_token: str = kwargs.get('session_token', '')
        self.expires_at: Optional[datetime] = kwargs.get('expires_at')
        self.created_at: Optional[datetime] = kwargs.get('created_at')
    
    @classmethod
    def find_by_token(cls, token: str) -> Optional['UserSession']:
        """根据token查找会话"""
        sessions = cls.find_all("session_token = ?", (token,))
        return sessions[0] if sessions else None
    
    def is_valid(self) -> bool:
        """检查会话是否有效"""
        if not self.expires_at:
            return False
        return datetime.now() < self.expires_at
    
    def get_user(self) -> Optional[User]:
        """获取会话对应的用户"""
        return User.find_by_id(self.user_id)
    
    def _insert(self) -> bool:
        """插入新会话"""
        query = """
            INSERT INTO user_sessions (user_id, session_token, expires_at)
            VALUES (?, ?, ?)
        """
        params = (self.user_id, self.session_token, self.expires_at)
        
        try:
            self.id = self.db.execute_insert(query, params)
            return True
        except Exception as e:
            print(f"插入会话失败: {e}")
            return False
    
    def _update(self) -> bool:
        """更新会话"""
        query = """
            UPDATE user_sessions 
            SET expires_at=?
            WHERE id=?
        """
        params = (self.expires_at, self.id)
        
        try:
            rowcount = self.db.execute_update(query, params)
            return rowcount > 0
        except Exception as e:
            print(f"更新会话失败: {e}")
            return False
    
    @classmethod
    def cleanup_expired(cls) -> int:
        """清理过期会话"""
        db = DatabaseManager()
        query = "DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP"
        return db.execute_update(query)
