"""
文档服务（简化版）
"""

from typing import List, Optional, Dict, Any
import os
from pathlib import Path

class DocumentService:
    """文档服务类"""
    
    def __init__(self):
        self.document_root = Path(__file__).parent.parent.parent / "documents"
        self.document_root.mkdir(exist_ok=True)
    
    def list_documents(self, category: str = None) -> List[Dict[str, Any]]:
        """列出文档"""
        documents = []
        
        for file_path in self.document_root.rglob("*"):
            if file_path.is_file():
                relative_path = file_path.relative_to(self.document_root)
                documents.append({
                    'name': file_path.name,
                    'path': str(relative_path),
                    'size': file_path.stat().st_size,
                    'modified': file_path.stat().st_mtime,
                    'category': str(relative_path.parent) if relative_path.parent != Path('.') else 'root'
                })
        
        if category and category != 'all':
            documents = [doc for doc in documents if doc['category'] == category]
        
        return documents
    
    def get_categories(self) -> List[str]:
        """获取文档分类"""
        categories = set()
        
        for file_path in self.document_root.rglob("*"):
            if file_path.is_file():
                relative_path = file_path.relative_to(self.document_root)
                if relative_path.parent != Path('.'):
                    categories.add(str(relative_path.parent))
        
        return sorted(list(categories))
    
    def upload_document(self, file_data: bytes, filename: str, category: str = None) -> bool:
        """上传文档"""
        try:
            if category:
                target_dir = self.document_root / category
                target_dir.mkdir(parents=True, exist_ok=True)
                target_path = target_dir / filename
            else:
                target_path = self.document_root / filename
            
            with open(target_path, 'wb') as f:
                f.write(file_data)
            
            return True
        except Exception as e:
            print(f"上传文档失败: {e}")
            return False
    
    def delete_document(self, file_path: str) -> bool:
        """删除文档"""
        try:
            full_path = self.document_root / file_path
            if full_path.exists() and full_path.is_file():
                full_path.unlink()
                return True
            return False
        except Exception as e:
            print(f"删除文档失败: {e}")
            return False
