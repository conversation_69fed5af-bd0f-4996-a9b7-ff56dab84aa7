// 综合管理系统前端应用
class ManagementApp {
    constructor() {
        this.currentUser = null;
        this.apiBaseUrl = 'http://localhost:8501'; // Streamlit默认端口
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkAuthStatus();
    }

    bindEvents() {
        // 登录表单
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // 注册表单
        document.getElementById('registerForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // 页面切换
        document.getElementById('registerBtn').addEventListener('click', () => {
            this.showPage('register');
        });

        document.getElementById('backToLogin').addEventListener('click', () => {
            this.showPage('login');
        });

        // 初始化数据库
        document.getElementById('initDbBtn').addEventListener('click', () => {
            this.initDatabase();
        });

        // 密码确认验证
        document.getElementById('regConfirmPassword').addEventListener('input', () => {
            this.validatePasswordMatch();
        });
    }

    showPage(pageName) {
        // 隐藏所有页面
        document.getElementById('loginPage').classList.add('hidden');
        document.getElementById('registerPage').classList.add('hidden');
        document.getElementById('mainApp').classList.add('hidden');

        // 显示指定页面
        switch(pageName) {
            case 'login':
                document.getElementById('loginPage').classList.remove('hidden');
                break;
            case 'register':
                document.getElementById('registerPage').classList.remove('hidden');
                break;
            case 'main':
                document.getElementById('mainApp').classList.remove('hidden');
                this.loadStreamlitApp();
                break;
        }
    }

    async handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        if (!username || !password) {
            this.showMessage('loginMessage', '请输入用户名和密码', 'error');
            return;
        }

        try {
            // 这里应该调用后端API进行认证
            // 暂时使用模拟认证
            if (await this.authenticateUser(username, password)) {
                this.showMessage('loginMessage', '登录成功！正在跳转...', 'success');
                setTimeout(() => {
                    this.showPage('main');
                }, 1000);
            } else {
                this.showMessage('loginMessage', '用户名或密码错误', 'error');
            }
        } catch (error) {
            console.error('登录错误:', error);
            this.showMessage('loginMessage', '登录失败，请稍后重试', 'error');
        }
    }

    async handleRegister() {
        const formData = new FormData(document.getElementById('registerForm'));
        const data = Object.fromEntries(formData);

        // 验证表单
        if (!this.validateRegisterForm(data)) {
            return;
        }

        try {
            // 这里应该调用后端API进行注册
            if (await this.registerUser(data)) {
                this.showMessage('registerMessage', '注册成功！请返回登录', 'success');
                setTimeout(() => {
                    this.showPage('login');
                }, 2000);
            } else {
                this.showMessage('registerMessage', '注册失败，用户名或邮箱已存在', 'error');
            }
        } catch (error) {
            console.error('注册错误:', error);
            this.showMessage('registerMessage', '注册失败，请稍后重试', 'error');
        }
    }

    validateRegisterForm(data) {
        // 检查必填字段
        if (!data.username || !data.email || !data.fullName || !data.password) {
            this.showMessage('registerMessage', '请填写所有必填字段', 'error');
            return false;
        }

        // 检查邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
            this.showMessage('registerMessage', '请输入有效的邮箱地址', 'error');
            return false;
        }

        // 检查密码长度
        if (data.password.length < 6) {
            this.showMessage('registerMessage', '密码长度至少6位', 'error');
            return false;
        }

        // 检查密码确认
        if (data.password !== data.confirmPassword) {
            this.showMessage('registerMessage', '两次输入的密码不一致', 'error');
            return false;
        }

        // 检查用户协议
        if (!document.getElementById('agreeTerms').checked) {
            this.showMessage('registerMessage', '请同意用户协议和隐私政策', 'error');
            return false;
        }

        return true;
    }

    validatePasswordMatch() {
        const password = document.getElementById('regPassword').value;
        const confirmPassword = document.getElementById('regConfirmPassword').value;
        const confirmInput = document.getElementById('regConfirmPassword');

        if (confirmPassword && password !== confirmPassword) {
            confirmInput.classList.add('border-red-500');
            confirmInput.classList.remove('border-gray-200');
        } else {
            confirmInput.classList.remove('border-red-500');
            confirmInput.classList.add('border-gray-200');
        }
    }

    async authenticateUser(username, password) {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                // 简单的模拟认证逻辑
                if ((username === 'admin' && password === 'admin123') ||
                    (username === 'yangjun' && password === '123456')) {
                    this.currentUser = { username, fullName: username === 'admin' ? '管理员' : '杨俊' };
                    localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
                    resolve(true);
                } else {
                    resolve(false);
                }
            }, 1000);
        });
    }

    async registerUser(data) {
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                // 简单的模拟注册逻辑
                const existingUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
                
                // 检查用户名和邮箱是否已存在
                const userExists = existingUsers.some(user => 
                    user.username === data.username || user.email === data.email
                );

                if (userExists) {
                    resolve(false);
                } else {
                    existingUsers.push({
                        username: data.username,
                        email: data.email,
                        fullName: data.fullName,
                        department: data.department,
                        position: data.position,
                        createdAt: new Date().toISOString()
                    });
                    localStorage.setItem('registeredUsers', JSON.stringify(existingUsers));
                    resolve(true);
                }
            }, 1000);
        });
    }

    async initDatabase() {
        try {
            this.showMessage('loginMessage', '正在初始化数据库...', 'info');
            
            // 这里应该调用后端API初始化数据库
            // 暂时使用模拟
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            this.showMessage('loginMessage', '数据库初始化成功！\n管理员账号：admin / admin123\n测试账号：yangjun / 123456', 'success');
        } catch (error) {
            console.error('数据库初始化错误:', error);
            this.showMessage('loginMessage', '数据库初始化失败', 'error');
        }
    }

    checkAuthStatus() {
        const savedUser = localStorage.getItem('currentUser');
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);
            this.showPage('main');
        }
    }

    loadStreamlitApp() {
        // 加载Streamlit应用
        const mainApp = document.getElementById('mainApp');
        mainApp.innerHTML = `
            <div class="h-screen">
                <iframe 
                    src="${this.apiBaseUrl}" 
                    class="w-full h-full border-0"
                    title="综合管理系统">
                </iframe>
            </div>
        `;
    }

    showMessage(containerId, message, type) {
        const container = document.getElementById(containerId);
        const messageElement = container.querySelector('p');
        
        container.classList.remove('hidden', 'bg-red-100', 'bg-green-100', 'bg-blue-100', 'text-red-700', 'text-green-700', 'text-blue-700');
        
        switch(type) {
            case 'error':
                container.classList.add('bg-red-100', 'text-red-700');
                break;
            case 'success':
                container.classList.add('bg-green-100', 'text-green-700');
                break;
            case 'info':
                container.classList.add('bg-blue-100', 'text-blue-700');
                break;
        }
        
        messageElement.textContent = message;
        container.classList.remove('hidden');
        
        // 自动隐藏消息（除了成功消息）
        if (type !== 'success') {
            setTimeout(() => {
                container.classList.add('hidden');
            }, 5000);
        }
    }

    showToast(title, message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastIcon = document.getElementById('toastIcon');
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');

        // 设置图标
        let iconClass = '';
        switch(type) {
            case 'success':
                iconClass = 'fas fa-check-circle text-green-500';
                break;
            case 'error':
                iconClass = 'fas fa-exclamation-circle text-red-500';
                break;
            case 'warning':
                iconClass = 'fas fa-exclamation-triangle text-yellow-500';
                break;
            default:
                iconClass = 'fas fa-info-circle text-blue-500';
        }

        toastIcon.className = iconClass;
        toastTitle.textContent = title;
        toastMessage.textContent = message;

        // 显示Toast
        toast.classList.remove('hidden');
        toast.classList.add('fade-in');

        // 自动隐藏
        setTimeout(() => {
            toast.classList.add('hidden');
            toast.classList.remove('fade-in');
        }, 4000);
    }

    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        this.showPage('login');
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new ManagementApp();
});
