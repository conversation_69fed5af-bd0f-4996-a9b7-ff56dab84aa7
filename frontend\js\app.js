// 综合管理系统前端应用
class ManagementApp {
    constructor() {
        this.currentUser = null;
        this.apiBaseUrl = 'http://localhost:3000/api'; // Express API端点
        this.token = localStorage.getItem('authToken');
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkAuthStatus();
    }

    bindEvents() {
        // 登录表单
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // 注册表单
        document.getElementById('registerForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // 页面切换
        document.getElementById('registerBtn').addEventListener('click', () => {
            this.showPage('register');
        });

        document.getElementById('backToLogin').addEventListener('click', () => {
            this.showPage('login');
        });

        // 初始化数据库
        document.getElementById('initDbBtn').addEventListener('click', () => {
            this.initDatabase();
        });

        // 密码确认验证
        document.getElementById('regConfirmPassword').addEventListener('input', () => {
            this.validatePasswordMatch();
        });
    }

    showPage(pageName) {
        // 隐藏所有页面
        document.getElementById('loginPage').classList.add('hidden');
        document.getElementById('registerPage').classList.add('hidden');
        document.getElementById('mainApp').classList.add('hidden');

        // 显示指定页面
        switch(pageName) {
            case 'login':
                document.getElementById('loginPage').classList.remove('hidden');
                break;
            case 'register':
                document.getElementById('registerPage').classList.remove('hidden');
                break;
            case 'main':
                document.getElementById('mainApp').classList.remove('hidden');
                this.loadStreamlitApp();
                break;
        }
    }

    async handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        if (!username || !password) {
            this.showMessage('loginMessage', '请输入用户名和密码', 'error');
            return;
        }

        try {
            // 这里应该调用后端API进行认证
            // 暂时使用模拟认证
            if (await this.authenticateUser(username, password)) {
                this.showMessage('loginMessage', '登录成功！正在跳转...', 'success');
                setTimeout(() => {
                    this.showPage('main');
                }, 1000);
            } else {
                this.showMessage('loginMessage', '用户名或密码错误', 'error');
            }
        } catch (error) {
            console.error('登录错误:', error);
            this.showMessage('loginMessage', '登录失败，请稍后重试', 'error');
        }
    }

    async handleRegister() {
        const formData = new FormData(document.getElementById('registerForm'));
        const data = Object.fromEntries(formData);

        // 验证表单
        if (!this.validateRegisterForm(data)) {
            return;
        }

        try {
            // 这里应该调用后端API进行注册
            if (await this.registerUser(data)) {
                this.showMessage('registerMessage', '注册成功！请返回登录', 'success');
                setTimeout(() => {
                    this.showPage('login');
                }, 2000);
            } else {
                this.showMessage('registerMessage', '注册失败，用户名或邮箱已存在', 'error');
            }
        } catch (error) {
            console.error('注册错误:', error);
            this.showMessage('registerMessage', '注册失败，请稍后重试', 'error');
        }
    }

    validateRegisterForm(data) {
        // 检查必填字段
        if (!data.username || !data.email || !data.fullName || !data.password) {
            this.showMessage('registerMessage', '请填写所有必填字段', 'error');
            return false;
        }

        // 检查邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
            this.showMessage('registerMessage', '请输入有效的邮箱地址', 'error');
            return false;
        }

        // 检查密码长度
        if (data.password.length < 6) {
            this.showMessage('registerMessage', '密码长度至少6位', 'error');
            return false;
        }

        // 检查密码确认
        if (data.password !== data.confirmPassword) {
            this.showMessage('registerMessage', '两次输入的密码不一致', 'error');
            return false;
        }

        // 检查用户协议
        if (!document.getElementById('agreeTerms').checked) {
            this.showMessage('registerMessage', '请同意用户协议和隐私政策', 'error');
            return false;
        }

        return true;
    }

    validatePasswordMatch() {
        const password = document.getElementById('regPassword').value;
        const confirmPassword = document.getElementById('regConfirmPassword').value;
        const confirmInput = document.getElementById('regConfirmPassword');

        if (confirmPassword && password !== confirmPassword) {
            confirmInput.classList.add('border-red-500');
            confirmInput.classList.remove('border-gray-200');
        } else {
            confirmInput.classList.remove('border-red-500');
            confirmInput.classList.add('border-gray-200');
        }
    }

    async authenticateUser(username, password) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                this.token = data.data.token;
                this.currentUser = data.data.user;
                localStorage.setItem('authToken', this.token);
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
                return true;
            } else {
                console.error('登录失败:', data.message);
                return false;
            }
        } catch (error) {
            console.error('登录请求失败:', error);
            return false;
        }
    }

    async registerUser(data) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: data.username,
                    email: data.email,
                    full_name: data.fullName,
                    department: data.department,
                    position: data.position,
                    password: data.password
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                return true;
            } else {
                console.error('注册失败:', result.message);
                return false;
            }
        } catch (error) {
            console.error('注册请求失败:', error);
            return false;
        }
    }

    async initDatabase() {
        try {
            this.showMessage('loginMessage', '正在初始化数据库...', 'info');

            const response = await fetch(`${this.apiBaseUrl}/auth/init-db`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showMessage('loginMessage', '数据库初始化成功！\n管理员账号：admin / admin123\n测试账号：yangjun / 123456', 'success');
            } else {
                this.showMessage('loginMessage', result.message || '数据库初始化失败', 'error');
            }
        } catch (error) {
            console.error('数据库初始化错误:', error);
            this.showMessage('loginMessage', '数据库初始化失败，请检查后端服务是否启动', 'error');
        }
    }

    async checkAuthStatus() {
        const savedUser = localStorage.getItem('currentUser');
        const savedToken = localStorage.getItem('authToken');

        if (savedUser && savedToken) {
            this.currentUser = JSON.parse(savedUser);
            this.token = savedToken;

            // 验证token是否仍然有效
            try {
                const response = await fetch(`${this.apiBaseUrl}/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${this.token}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        this.currentUser = result.data;
                        localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
                        this.showPage('main');
                        return;
                    }
                }
            } catch (error) {
                console.error('Token验证失败:', error);
            }

            // Token无效，清除本地存储
            this.logout();
        }
    }

    async loadStreamlitApp() {
        // 加载管理界面并获取实时数据
        const mainApp = document.getElementById('mainApp');

        mainApp.innerHTML = `
            <div class="h-screen bg-gray-50 p-4">
                <div class="max-w-7xl mx-auto">
                    <!-- 顶部导航 -->
                    <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                        <div class="flex items-center justify-between">
                            <h1 class="text-2xl font-bold text-gray-800">📊 综合管理系统</h1>
                            <div class="flex items-center space-x-4">
                                <span class="text-sm text-gray-600">欢迎，${this.currentUser.full_name || this.currentUser.fullName}</span>
                                <button onclick="app.logout()" class="bg-red-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-600">
                                    退出登录
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 仪表板 -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                    <i class="fas fa-users text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">总用户数</p>
                                    <p class="text-2xl font-semibold text-gray-900" id="totalUsers">-</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600">
                                    <i class="fas fa-clipboard-list text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">本周日志</p>
                                    <p class="text-2xl font-semibold text-gray-900" id="weeklyLogs">-</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                    <i class="fas fa-project-diagram text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">活跃项目</p>
                                    <p class="text-2xl font-semibold text-gray-900" id="activeProjects">-</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                                    <i class="fas fa-chart-line text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">本月日志</p>
                                    <p class="text-2xl font-semibold text-gray-900" id="monthlyLogs">-</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 功能模块 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">📝 工作日志管理</h3>
                            <p class="text-gray-600 mb-4">记录和管理日常工作内容，追踪完成情况</p>
                            <button id="workLogBtn" class="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors duration-200 active:scale-95 transform">
                                进入模块
                            </button>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">💼 特殊项目管理</h3>
                            <p class="text-gray-600 mb-4">管理特殊项目信息和财务数据</p>
                            <button id="projectBtn" class="w-full bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition-colors duration-200 active:scale-95 transform">
                                进入模块
                            </button>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">📊 统计分析</h3>
                            <p class="text-gray-600 mb-4">查看各类统计报表和数据分析</p>
                            <button id="statisticsBtn" class="w-full bg-purple-500 text-white py-2 px-4 rounded-lg hover:bg-purple-600 transition-colors duration-200 active:scale-95 transform">
                                进入模块
                            </button>
                        </div>
                    </div>

                    <!-- 提示信息 -->
                    <div class="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="fas fa-check-circle text-green-600 mt-1"></i>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800">Node.js版本</h3>
                                <p class="text-sm text-green-700 mt-1">
                                    系统已成功转换为Node.js + Express架构，提供完整的API功能。
                                    <br>详细说明请查看 <strong>README-nodejs.md</strong> 文件。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 绑定功能模块按钮事件
        this.bindModuleEvents();

        // 加载实时数据
        this.loadDashboardData();
    }

    bindModuleEvents() {
        // 工作日志管理按钮
        document.getElementById('workLogBtn').addEventListener('click', () => {
            this.showWorkLogModule();
        });

        // 特殊项目管理按钮
        document.getElementById('projectBtn').addEventListener('click', () => {
            this.showProjectModule();
        });

        // 统计分析按钮
        document.getElementById('statisticsBtn').addEventListener('click', () => {
            this.showStatisticsModule();
        });
    }

    showWorkLogModule() {
        const mainApp = document.getElementById('mainApp');
        mainApp.innerHTML = `
            <div class="h-screen bg-gray-50 p-4">
                <div class="max-w-7xl mx-auto">
                    <!-- 顶部导航 -->
                    <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button id="backToDashboard" class="text-gray-600 hover:text-gray-800">
                                    <i class="fas fa-arrow-left text-xl"></i>
                                </button>
                                <h1 class="text-2xl font-bold text-gray-800">📝 工作日志管理</h1>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="text-sm text-gray-600">欢迎，${this.currentUser.full_name || this.currentUser.fullName}</span>
                                <button onclick="app.logout()" class="bg-red-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-600">
                                    退出登录
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 工作日志内容 -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-semibold text-gray-800">工作日志列表</h2>
                            <button id="addWorkLogBtn" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                                <i class="fas fa-plus mr-2"></i>新增日志
                            </button>
                        </div>

                        <div id="workLogList" class="space-y-4">
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-clipboard-list text-4xl mb-4"></i>
                                <p>暂无工作日志，点击"新增日志"开始记录</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 绑定返回按钮事件
        document.getElementById('backToDashboard').addEventListener('click', () => {
            this.loadStreamlitApp();
        });

        // 绑定新增日志按钮事件
        document.getElementById('addWorkLogBtn').addEventListener('click', () => {
            this.showAddWorkLogForm();
        });

        // 加载工作日志数据
        this.loadWorkLogs();
    }

    showProjectModule() {
        const mainApp = document.getElementById('mainApp');
        mainApp.innerHTML = `
            <div class="h-screen bg-gray-50 p-4">
                <div class="max-w-7xl mx-auto">
                    <!-- 顶部导航 -->
                    <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button id="backToDashboard" class="text-gray-600 hover:text-gray-800">
                                    <i class="fas fa-arrow-left text-xl"></i>
                                </button>
                                <h1 class="text-2xl font-bold text-gray-800">💼 特殊项目管理</h1>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="text-sm text-gray-600">欢迎，${this.currentUser.full_name || this.currentUser.fullName}</span>
                                <button onclick="app.logout()" class="bg-red-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-600">
                                    退出登录
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 项目管理内容 -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-semibold text-gray-800">项目列表</h2>
                            <button id="addProjectBtn" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                                <i class="fas fa-plus mr-2"></i>新增项目
                            </button>
                        </div>

                        <div id="projectList" class="space-y-4">
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-briefcase text-4xl mb-4"></i>
                                <p>暂无特殊项目，点击"新增项目"开始管理</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 绑定返回按钮事件
        document.getElementById('backToDashboard').addEventListener('click', () => {
            this.loadStreamlitApp();
        });

        // 绑定新增项目按钮事件
        document.getElementById('addProjectBtn').addEventListener('click', () => {
            this.showAddProjectForm();
        });

        // 加载项目数据
        this.loadProjects();
    }

    showStatisticsModule() {
        const mainApp = document.getElementById('mainApp');
        mainApp.innerHTML = `
            <div class="h-screen bg-gray-50 p-4">
                <div class="max-w-7xl mx-auto">
                    <!-- 顶部导航 -->
                    <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button id="backToDashboard" class="text-gray-600 hover:text-gray-800">
                                    <i class="fas fa-arrow-left text-xl"></i>
                                </button>
                                <h1 class="text-2xl font-bold text-gray-800">📊 统计分析</h1>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="text-sm text-gray-600">欢迎，${this.currentUser.full_name || this.currentUser.fullName}</span>
                                <button onclick="app.logout()" class="bg-red-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-600">
                                    退出登录
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 统计分析内容 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">工作日志统计</h3>
                            <div id="workLogStats" class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">总日志数:</span>
                                    <span class="font-semibold">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">本月日志:</span>
                                    <span class="font-semibold">0</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">项目统计</h3>
                            <div id="projectStats" class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">总项目数:</span>
                                    <span class="font-semibold">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">进行中:</span>
                                    <span class="font-semibold">0</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">用户统计</h3>
                            <div id="userStats" class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">总用户数:</span>
                                    <span class="font-semibold">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">活跃用户:</span>
                                    <span class="font-semibold">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 绑定返回按钮事件
        document.getElementById('backToDashboard').addEventListener('click', () => {
            this.loadStreamlitApp();
        });

        // 加载统计数据
        this.loadStatistics();
    }

    showMessage(containerId, message, type) {
        const container = document.getElementById(containerId);
        const messageElement = container.querySelector('p');
        
        container.classList.remove('hidden', 'bg-red-100', 'bg-green-100', 'bg-blue-100', 'text-red-700', 'text-green-700', 'text-blue-700');
        
        switch(type) {
            case 'error':
                container.classList.add('bg-red-100', 'text-red-700');
                break;
            case 'success':
                container.classList.add('bg-green-100', 'text-green-700');
                break;
            case 'info':
                container.classList.add('bg-blue-100', 'text-blue-700');
                break;
        }
        
        messageElement.textContent = message;
        container.classList.remove('hidden');
        
        // 自动隐藏消息（除了成功消息）
        if (type !== 'success') {
            setTimeout(() => {
                container.classList.add('hidden');
            }, 5000);
        }
    }

    showToast(title, message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastIcon = document.getElementById('toastIcon');
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');

        // 设置图标
        let iconClass = '';
        switch(type) {
            case 'success':
                iconClass = 'fas fa-check-circle text-green-500';
                break;
            case 'error':
                iconClass = 'fas fa-exclamation-circle text-red-500';
                break;
            case 'warning':
                iconClass = 'fas fa-exclamation-triangle text-yellow-500';
                break;
            default:
                iconClass = 'fas fa-info-circle text-blue-500';
        }

        toastIcon.className = iconClass;
        toastTitle.textContent = title;
        toastMessage.textContent = message;

        // 显示Toast
        toast.classList.remove('hidden');
        toast.classList.add('fade-in');

        // 自动隐藏
        setTimeout(() => {
            toast.classList.add('hidden');
            toast.classList.remove('fade-in');
        }, 4000);
    }

    async loadDashboardData() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/statistics/overview`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    const data = result.data;
                    document.getElementById('totalUsers').textContent = data.totalUsers || 0;
                    document.getElementById('weeklyLogs').textContent = data.weeklyLogs || 0;
                    document.getElementById('activeProjects').textContent = data.activeProjects || 0;
                    document.getElementById('monthlyLogs').textContent = data.monthlyLogs || 0;
                }
            }
        } catch (error) {
            console.error('加载仪表板数据失败:', error);
        }
    }

    async loadWorkLogs() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/work-logs`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.displayWorkLogs(data.data || []);
            } else {
                console.error('加载工作日志失败');
            }
        } catch (error) {
            console.error('加载工作日志错误:', error);
        }
    }

    displayWorkLogs(workLogs) {
        const container = document.getElementById('workLogList');
        if (workLogs.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-clipboard-list text-4xl mb-4"></i>
                    <p>暂无工作日志，点击"新增日志"开始记录</p>
                </div>
            `;
            return;
        }

        container.innerHTML = workLogs.map(log => `
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="font-semibold text-gray-800">${log.title || '工作日志'}</h3>
                        <p class="text-gray-600 mt-1">${log.content || '无内容'}</p>
                        <p class="text-sm text-gray-500 mt-2">
                            <i class="fas fa-calendar mr-1"></i>
                            ${log.work_date || '未知日期'}
                        </p>
                    </div>
                    <div class="flex space-x-2">
                        <button class="text-blue-500 hover:text-blue-700">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="text-red-500 hover:text-red-700">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    async loadProjects() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/projects`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.displayProjects(data.data || []);
            } else {
                console.error('加载项目失败');
            }
        } catch (error) {
            console.error('加载项目错误:', error);
        }
    }

    displayProjects(projects) {
        const container = document.getElementById('projectList');
        if (projects.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-briefcase text-4xl mb-4"></i>
                    <p>暂无特殊项目，点击"新增项目"开始管理</p>
                </div>
            `;
            return;
        }

        container.innerHTML = projects.map(project => `
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="font-semibold text-gray-800">${project.name || '未命名项目'}</h3>
                        <p class="text-gray-600 mt-1">${project.description || '无描述'}</p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="text-sm text-gray-500">
                                <i class="fas fa-calendar mr-1"></i>
                                ${project.start_date || '未知开始日期'}
                            </span>
                            <span class="text-sm px-2 py-1 rounded-full ${project.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                ${project.status === 'active' ? '进行中' : '已完成'}
                            </span>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="text-blue-500 hover:text-blue-700">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="text-red-500 hover:text-red-700">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    async loadStatistics() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/statistics/overview`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.displayStatistics(data.data || {});
            } else {
                console.error('加载统计数据失败');
            }
        } catch (error) {
            console.error('加载统计数据错误:', error);
        }
    }

    displayStatistics(stats) {
        // 更新工作日志统计
        const workLogStats = document.getElementById('workLogStats');
        if (workLogStats) {
            workLogStats.innerHTML = `
                <div class="flex justify-between">
                    <span class="text-gray-600">总日志数:</span>
                    <span class="font-semibold">${stats.totalWorkLogs || 0}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">本月日志:</span>
                    <span class="font-semibold">${stats.monthlyWorkLogs || 0}</span>
                </div>
            `;
        }

        // 更新项目统计
        const projectStats = document.getElementById('projectStats');
        if (projectStats) {
            projectStats.innerHTML = `
                <div class="flex justify-between">
                    <span class="text-gray-600">总项目数:</span>
                    <span class="font-semibold">${stats.totalProjects || 0}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">进行中:</span>
                    <span class="font-semibold">${stats.activeProjects || 0}</span>
                </div>
            `;
        }

        // 更新用户统计
        const userStats = document.getElementById('userStats');
        if (userStats) {
            userStats.innerHTML = `
                <div class="flex justify-between">
                    <span class="text-gray-600">总用户数:</span>
                    <span class="font-semibold">${stats.totalUsers || 0}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">活跃用户:</span>
                    <span class="font-semibold">${stats.activeUsers || 0}</span>
                </div>
            `;
        }
    }

    showAddWorkLogForm() {
        // 显示新增工作日志表单的占位符
        this.showToast('功能开发中', '新增工作日志功能正在开发中...', 'info');
    }

    showAddProjectForm() {
        // 显示新增项目表单的占位符
        this.showToast('功能开发中', '新增项目功能正在开发中...', 'info');
    }

    async logout() {
        try {
            // 调用后端登出API
            await fetch(`${this.apiBaseUrl}/auth/logout`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });
        } catch (error) {
            console.error('登出请求失败:', error);
        }

        // 清除本地存储
        this.currentUser = null;
        this.token = null;
        localStorage.removeItem('currentUser');
        localStorage.removeItem('authToken');
        this.showPage('login');
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new ManagementApp();
});
