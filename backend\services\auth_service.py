"""
认证服务
"""

from typing import Optional
from backend.models.user import User, UserSession
from backend.models.audit_log import AuditLog

class AuthService:
    """认证服务类"""
    
    def authenticate(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        user = User.find_by_username(username)
        
        if user and user.is_active and user.verify_password(password):
            # 记录登录日志
            self._log_action(user.id, "login", "user", user.id, {"username": username})
            return user
        
        # 记录登录失败日志
        self._log_action(None, "login_failed", "user", None, {"username": username})
        return None
    
    def register(self, username: str, email: str, password: str, full_name: str, 
                department: str = None, position: str = None) -> Optional[User]:
        """用户注册"""
        # 检查用户名是否已存在
        if User.find_by_username(username):
            return None
        
        # 检查邮箱是否已存在
        if User.find_by_email(email):
            return None
        
        # 创建新用户
        user = User()
        user.username = username
        user.email = email
        user.set_password(password)
        user.full_name = full_name
        user.department = department
        user.position = position
        
        if user.save():
            # 记录注册日志
            self._log_action(user.id, "register", "user", user.id, {
                "username": username,
                "email": email,
                "full_name": full_name
            })
            return user
        
        return None
    
    def create_session(self, user: User) -> Optional[UserSession]:
        """创建用户会话"""
        session = user.create_session()
        if session:
            self._log_action(user.id, "create_session", "session", session.id)
        return session
    
    def validate_session(self, token: str) -> Optional[User]:
        """验证会话token"""
        session = UserSession.find_by_token(token)
        
        if session and session.is_valid():
            return session.get_user()
        
        return None
    
    def logout(self, user: User, token: str = None) -> bool:
        """用户登出"""
        if token:
            session = UserSession.find_by_token(token)
            if session:
                session.delete()
        
        # 记录登出日志
        self._log_action(user.id, "logout", "user", user.id)
        return True
    
    def change_password(self, user: User, old_password: str, new_password: str) -> bool:
        """修改密码"""
        if not user.verify_password(old_password):
            return False
        
        user.set_password(new_password)
        if user.save():
            # 记录密码修改日志
            self._log_action(user.id, "change_password", "user", user.id)
            return True
        
        return False
    
    def update_profile(self, user: User, **kwargs) -> bool:
        """更新用户资料"""
        allowed_fields = ['full_name', 'department', 'position', 'phone']
        
        for field, value in kwargs.items():
            if field in allowed_fields:
                setattr(user, field, value)
        
        if user.save():
            # 记录资料更新日志
            self._log_action(user.id, "update_profile", "user", user.id, kwargs)
            return True
        
        return False
    
    def _log_action(self, user_id: Optional[int], action: str, resource_type: str = None, 
                   resource_id: int = None, details: dict = None):
        """记录操作日志"""
        try:
            audit_log = AuditLog()
            audit_log.user_id = user_id
            audit_log.action = action
            audit_log.resource_type = resource_type
            audit_log.resource_id = resource_id
            audit_log.details = details
            audit_log.save()
        except Exception as e:
            print(f"记录操作日志失败: {e}")
    
    @staticmethod
    def cleanup_expired_sessions() -> int:
        """清理过期会话"""
        return UserSession.cleanup_expired()
