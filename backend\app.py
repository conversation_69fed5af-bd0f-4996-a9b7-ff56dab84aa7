#!/usr/bin/env python3
"""
综合管理系统 - Streamlit主应用
"""

import streamlit as st
import sys
from pathlib import Path
import pandas as pd
from datetime import datetime, date, timedelta
import plotly.express as px
import plotly.graph_objects as go
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from backend.models.user import User, UserSession
from backend.models.work_log import WorkLog
from backend.models.special_project import SpecialProject
from backend.services.auth_service import AuthService
from backend.services.work_log_service import WorkLogService
from backend.services.stats_service import StatsService
from backend.utils.init_db import init_database

# 页面配置
st.set_page_config(
    page_title="综合管理系统",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-message {
        color: #28a745;
        font-weight: bold;
    }
    .error-message {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """初始化会话状态"""
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    if 'user' not in st.session_state:
        st.session_state.user = None
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'login'

def show_login_page():
    """显示登录页面"""
    st.markdown('<h1 class="main-header">综合管理系统</h1>', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.subheader("用户登录")
        
        with st.form("login_form"):
            username = st.text_input("用户名", placeholder="请输入用户名")
            password = st.text_input("密码", type="password", placeholder="请输入密码")
            submitted = st.form_submit_button("登录", use_container_width=True)
            
            if submitted:
                if username and password:
                    auth_service = AuthService()
                    user = auth_service.authenticate(username, password)
                    
                    if user:
                        st.session_state.authenticated = True
                        st.session_state.user = user
                        st.session_state.current_page = 'dashboard'
                        st.success("登录成功！")
                        st.rerun()
                    else:
                        st.error("用户名或密码错误")
                else:
                    st.error("请输入用户名和密码")
        
        # 初始化数据库按钮
        if st.button("初始化数据库", help="首次使用时点击此按钮初始化数据库"):
            try:
                init_database()
                st.success("数据库初始化成功！请使用以下账号登录：\n\n管理员：admin / admin123\n测试用户：yangjun / 123456")
            except Exception as e:
                st.error(f"数据库初始化失败：{e}")

def show_sidebar():
    """显示侧边栏"""
    with st.sidebar:
        st.title("📊 综合管理系统")
        
        if st.session_state.user:
            st.write(f"欢迎，{st.session_state.user.full_name}")
            st.write(f"部门：{st.session_state.user.department}")
            
            st.divider()
            
            # 导航菜单
            pages = {
                "dashboard": "📈 仪表板",
                "work_logs": "📝 工作日志",
                "special_projects": "💼 特殊项目",
                "statistics": "📊 统计分析",
                "documents": "📁 文档管理",
                "settings": "⚙️ 系统设置"
            }
            
            for page_key, page_name in pages.items():
                if st.button(page_name, use_container_width=True):
                    st.session_state.current_page = page_key
                    st.rerun()
            
            st.divider()
            
            if st.button("退出登录", use_container_width=True):
                st.session_state.authenticated = False
                st.session_state.user = None
                st.session_state.current_page = 'login'
                st.rerun()

def show_dashboard():
    """显示仪表板"""
    st.title("📈 系统仪表板")
    
    # 统计卡片
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_users = User.count()
        st.metric("总用户数", total_users)
    
    with col2:
        today = date.today()
        today_logs = WorkLog.count("log_date = ?", (today,))
        st.metric("今日日志", today_logs)
    
    with col3:
        this_month = today.strftime("%Y-%m")
        month_projects = SpecialProject.count("month = ?", (this_month,))
        st.metric("本月项目", month_projects)
    
    with col4:
        active_users = User.count("is_active = 1")
        st.metric("活跃用户", active_users)
    
    st.divider()
    
    # 图表展示
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("最近7天工作日志统计")
        
        # 获取最近7天的日志统计
        stats_service = StatsService()
        log_stats = stats_service.get_recent_log_stats(7)
        
        if log_stats:
            df = pd.DataFrame(log_stats)
            fig = px.bar(df, x='date', y='count', title="每日日志数量")
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("暂无日志数据")
    
    with col2:
        st.subheader("部门用户分布")
        
        # 获取部门用户统计
        dept_stats = stats_service.get_department_user_stats()
        
        if dept_stats:
            df = pd.DataFrame(dept_stats)
            fig = px.pie(df, values='count', names='department', title="部门用户分布")
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("暂无用户数据")

def show_work_logs():
    """显示工作日志页面"""
    st.title("📝 工作日志管理")
    
    tab1, tab2, tab3 = st.tabs(["日志查询", "添加日志", "统计分析"])
    
    with tab1:
        st.subheader("工作日志查询")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            start_date = st.date_input("开始日期", value=date.today() - timedelta(days=7))
        
        with col2:
            end_date = st.date_input("结束日期", value=date.today())
        
        with col3:
            user_filter = st.selectbox("用户筛选", ["全部"] + [u.username for u in User.find_all()])
        
        if st.button("查询"):
            work_log_service = WorkLogService()
            logs = work_log_service.get_logs_by_date_range(start_date, end_date, user_filter)
            
            if logs:
                df = pd.DataFrame([log.to_dict() for log in logs])
                st.dataframe(df, use_container_width=True)
            else:
                st.info("未找到符合条件的日志")
    
    with tab2:
        st.subheader("添加工作日志")
        
        with st.form("add_log_form"):
            log_date = st.date_input("日期", value=date.today())
            content = st.text_area("工作内容", height=150, placeholder="请详细描述今日工作内容...")
            category = st.selectbox("分类", ["日常工作", "项目开发", "故障处理", "会议培训", "其他"])
            
            submitted = st.form_submit_button("提交日志")
            
            if submitted:
                if content.strip():
                    work_log_service = WorkLogService()
                    success = work_log_service.add_log(
                        st.session_state.user.id,
                        log_date,
                        content,
                        category
                    )
                    
                    if success:
                        st.success("日志提交成功！")
                    else:
                        st.error("日志提交失败，可能该日期已有日志记录")
                else:
                    st.error("请输入工作内容")
    
    with tab3:
        st.subheader("日志统计分析")
        
        stats_service = StatsService()
        
        # 用户完成率统计
        completion_stats = stats_service.get_user_completion_stats()
        
        if completion_stats:
            st.write("### 用户日志完成率")
            df = pd.DataFrame(completion_stats)
            
            fig = px.bar(
                df, 
                x='username', 
                y='completion_rate',
                title="用户日志完成率",
                labels={'completion_rate': '完成率 (%)', 'username': '用户名'}
            )
            st.plotly_chart(fig, use_container_width=True)
            
            # 显示详细数据
            st.dataframe(df, use_container_width=True)
        else:
            st.info("暂无统计数据")

def main():
    """主函数"""
    init_session_state()
    
    if not st.session_state.authenticated:
        show_login_page()
    else:
        show_sidebar()
        
        # 根据当前页面显示内容
        if st.session_state.current_page == 'dashboard':
            show_dashboard()
        elif st.session_state.current_page == 'work_logs':
            show_work_logs()
        elif st.session_state.current_page == 'special_projects':
            st.title("💼 特殊项目管理")
            st.info("特殊项目管理功能开发中...")
        elif st.session_state.current_page == 'statistics':
            st.title("📊 统计分析")
            st.info("统计分析功能开发中...")
        elif st.session_state.current_page == 'documents':
            st.title("📁 文档管理")
            st.info("文档管理功能开发中...")
        elif st.session_state.current_page == 'settings':
            st.title("⚙️ 系统设置")
            st.info("系统设置功能开发中...")

if __name__ == "__main__":
    main()
