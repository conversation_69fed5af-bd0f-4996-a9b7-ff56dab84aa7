#!/usr/bin/env python3
"""
系统测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_database_init():
    """测试数据库初始化"""
    try:
        from backend.utils.init_db import init_database
        print("正在测试数据库初始化...")
        init_database()
        print("✓ 数据库初始化成功")
        return True
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_models():
    """测试数据模型"""
    try:
        from backend.models import User, WorkLog, SpecialProject
        print("正在测试数据模型...")
        
        # 测试用户模型
        users = User.find_all()
        print(f"找到 {len(users)} 个用户")
        
        # 测试工作日志模型
        logs = WorkLog.find_all()
        print(f"找到 {len(logs)} 条工作日志")
        
        # 测试特殊项目模型
        projects = SpecialProject.find_all()
        print(f"找到 {len(projects)} 个特殊项目")
        
        print("✓ 数据模型测试成功")
        return True
    except Exception as e:
        print(f"✗ 数据模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_services():
    """测试业务服务"""
    try:
        from backend.services import AuthService, WorkLogService, StatsService
        print("正在测试业务服务...")
        
        # 测试认证服务
        auth_service = AuthService()
        user = auth_service.authenticate("admin", "admin123")
        if user:
            print(f"✓ 认证服务测试成功，用户: {user.username}")
        else:
            print("✗ 认证服务测试失败")
            return False
        
        # 测试工作日志服务
        work_log_service = WorkLogService()
        logs = work_log_service.get_logs_by_user(user.id, 5)
        print(f"✓ 工作日志服务测试成功，获取到 {len(logs)} 条日志")
        
        # 测试统计服务
        stats_service = StatsService()
        stats = stats_service.get_dashboard_summary()
        print(f"✓ 统计服务测试成功，基础统计: {stats['basic_stats']}")
        
        print("✓ 业务服务测试成功")
        return True
    except Exception as e:
        print(f"✗ 业务服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_frontend_files():
    """测试前端文件"""
    try:
        frontend_dir = project_root / "frontend"
        required_files = [
            "index.html",
            "css/ios-style.css", 
            "js/app.js"
        ]
        
        print("正在检查前端文件...")
        for file_path in required_files:
            full_path = frontend_dir / file_path
            if full_path.exists():
                print(f"✓ {file_path} 存在")
            else:
                print(f"✗ {file_path} 不存在")
                return False
        
        print("✓ 前端文件检查成功")
        return True
    except Exception as e:
        print(f"✗ 前端文件检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 综合管理系统测试")
    print("=" * 50)
    
    tests = [
        ("数据库初始化", test_database_init),
        ("数据模型", test_models),
        ("业务服务", test_services),
        ("前端文件", test_frontend_files)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
        
        print()
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常运行")
        print("\n启动建议:")
        print("1. 安装依赖: pip install streamlit pandas plotly")
        print("2. 启动后端: streamlit run backend/app.py")
        print("3. 启动前端: python -m http.server 8080 (在frontend目录下)")
        print("4. 访问: http://localhost:8080")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
