@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Python环境诊断与修复工具
echo ========================================
echo.

echo [1/6] 检查Python安装状态...
echo.

:: 检查Python是否在PATH中
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Python已安装并在PATH中
    python --version
    echo.
    goto :check_pip
) else (
    echo ✗ Python未找到或无法执行
    echo.
)

:: 检查常见Python安装路径
echo [2/6] 搜索已安装的Python...
set "python_found=0"

:: 检查用户AppData路径
if exist "%LOCALAPPDATA%\Programs\Python" (
    echo ✓ 发现Python安装: %LOCALAPPDATA%\Programs\Python
    set "python_found=1"
)

:: 检查系统Program Files路径
if exist "C:\Program Files\Python*" (
    echo ✓ 发现Python安装: C:\Program Files\Python*
    set "python_found=1"
)

:: 检查Windows Store Python
if exist "%LOCALAPPDATA%\Microsoft\WindowsApps\python.exe" (
    echo ⚠ 发现Windows Store Python (可能有问题): %LOCALAPPDATA%\Microsoft\WindowsApps\python.exe
    echo   建议卸载并安装官方版本
)

if !python_found! equ 0 (
    echo ✗ 未发现Python安装
    goto :install_python
)

echo.

:check_pip
echo [3/6] 检查pip状态...
pip --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ pip可用
    pip --version
) else (
    echo ✗ pip不可用
    echo 尝试修复pip...
    python -m ensurepip --upgrade >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✓ pip修复成功
    ) else (
        echo ✗ pip修复失败
    )
)

echo.

echo [4/6] 检查必需的包...
set "packages=streamlit pandas plotly"
for %%p in (%packages%) do (
    pip show %%p >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✓ %%p 已安装
    ) else (
        echo ✗ %%p 未安装
        echo   正在安装 %%p...
        pip install %%p
        if !errorlevel! equ 0 (
            echo ✓ %%p 安装成功
        ) else (
            echo ✗ %%p 安装失败
        )
    )
)

echo.

echo [5/6] 测试Python环境...
python -c "import sys; print('Python版本:', sys.version); print('Python路径:', sys.executable)" 2>nul
if %errorlevel% equ 0 (
    echo ✓ Python环境测试通过
) else (
    echo ✗ Python环境测试失败
    goto :install_python
)

echo.

echo [6/6] 测试必需包导入...
python -c "import streamlit, pandas, plotly; print('所有包导入成功')" 2>nul
if %errorlevel% equ 0 (
    echo ✓ 所有必需包可正常导入
    echo.
    echo ========================================
    echo 🎉 Python环境配置完成！
    echo ========================================
    echo.
    echo 下一步：运行 start_system.bat 启动系统
    goto :end
) else (
    echo ✗ 包导入失败
    echo 正在重新安装必需包...
    pip install --upgrade streamlit pandas plotly
)

goto :end

:install_python
echo.
echo ========================================
echo 需要安装Python
echo ========================================
echo.
echo 请按照以下步骤安装Python:
echo.
echo 1. 访问 https://www.python.org/downloads/
echo 2. 下载Python 3.10或更高版本
echo 3. 运行安装程序时：
echo    ✓ 勾选 "Add Python to PATH"
echo    ✓ 勾选 "Install for all users"
echo    ✓ 选择 "Customize installation"
echo    ✓ 确保包含 pip
echo 4. 安装完成后重新运行此脚本
echo.
echo 或者使用Anaconda:
echo 1. 访问 https://www.anaconda.com/products/distribution
echo 2. 下载并安装Anaconda
echo 3. 打开Anaconda Prompt
echo 4. 运行: conda create -n management python=3.10
echo 5. 运行: conda activate management
echo.

:end
echo.
pause
