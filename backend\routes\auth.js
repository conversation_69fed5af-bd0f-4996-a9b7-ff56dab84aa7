const express = require('express');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const { dbConnection } = require('../database/connection');
const { 
    generateToken, 
    generateRefreshToken, 
    verifyToken,
    createSession,
    destroySession,
    logAudit
} = require('../middleware/auth');
const { 
    asyncHandler, 
    sendSuccess, 
    ValidationError,
    AuthenticationError,
    handleValidationErrors
} = require('../middleware/errorHandler');

const router = express.Router();

/**
 * 用户登录
 * POST /api/auth/login
 */
router.post('/login', [
    body('username')
        .notEmpty()
        .withMessage('用户名不能为空')
        .isLength({ min: 3, max: 50 })
        .withMessage('用户名长度必须在3-50个字符之间'),
    body('password')
        .notEmpty()
        .withMessage('密码不能为空')
        .isLength({ min: 6 })
        .withMessage('密码长度至少6个字符')
], asyncHandler(async (req, res) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        throw handleValidationErrors(errors.array());
    }

    const { username, password } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');

    // 查找用户
    const user = await dbConnection.queryOne(
        'SELECT id, username, password_hash, full_name, role, status FROM users WHERE username = ?',
        [username]
    );

    if (!user) {
        // 记录失败的登录尝试
        await logAudit(null, `登录失败 - 用户不存在: ${username}`, null, null, null, null, ipAddress, userAgent);
        throw new AuthenticationError('用户名或密码错误');
    }

    // 检查用户状态
    if (user.status !== 'active') {
        await logAudit(user.id, `登录失败 - 用户已禁用: ${username}`, null, null, null, null, ipAddress, userAgent);
        throw new AuthenticationError('用户账号已被禁用');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
        await logAudit(user.id, `登录失败 - 密码错误: ${username}`, null, null, null, null, ipAddress, userAgent);
        throw new AuthenticationError('用户名或密码错误');
    }

    // 生成tokens
    const tokenPayload = {
        userId: user.id,
        username: user.username,
        role: user.role
    };

    const accessToken = generateToken(tokenPayload);
    const refreshToken = generateRefreshToken(tokenPayload);

    // 创建会话
    const sessionId = await createSession(user.id, accessToken, refreshToken, ipAddress, userAgent);

    // 更新最后登录时间
    await dbConnection.run(
        'UPDATE users SET last_login = datetime("now") WHERE id = ?',
        [user.id]
    );

    // 记录成功登录
    await logAudit(user.id, `用户登录成功: ${username}`, null, null, null, null, ipAddress, userAgent);

    // 返回响应
    sendSuccess(res, {
        user: {
            id: user.id,
            username: user.username,
            fullName: user.full_name,
            role: user.role
        },
        tokens: {
            accessToken,
            refreshToken,
            expiresIn: '24h'
        },
        sessionId
    }, '登录成功');
}));

/**
 * 用户登出
 * POST /api/auth/logout
 */
router.post('/logout', asyncHandler(async (req, res) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
        try {
            const decoded = verifyToken(token);
            
            // 销毁会话
            await destroySession(token);
            
            // 记录登出
            await logAudit(
                decoded.userId, 
                `用户登出: ${decoded.username}`, 
                null, null, null, null, 
                req.ip, 
                req.get('User-Agent')
            );
        } catch (error) {
            // Token无效也不影响登出操作
            console.log('登出时token验证失败:', error.message);
        }
    }

    sendSuccess(res, null, '登出成功');
}));

/**
 * 刷新token
 * POST /api/auth/refresh
 */
router.post('/refresh', [
    body('refreshToken')
        .notEmpty()
        .withMessage('刷新token不能为空')
], asyncHandler(async (req, res) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        throw handleValidationErrors(errors.array());
    }

    const { refreshToken } = req.body;

    try {
        // 验证刷新token
        const decoded = verifyToken(refreshToken);
        
        // 检查用户是否仍然存在且活跃
        const user = await dbConnection.queryOne(
            'SELECT id, username, full_name, role, status FROM users WHERE id = ? AND status = ?',
            [decoded.userId, 'active']
        );

        if (!user) {
            throw new AuthenticationError('用户不存在或已被禁用');
        }

        // 检查刷新token是否有效
        const session = await dbConnection.queryOne(
            'SELECT id FROM user_sessions WHERE user_id = ? AND refresh_token = ? AND is_active = 1',
            [user.id, refreshToken]
        );

        if (!session) {
            throw new AuthenticationError('刷新token无效或已过期');
        }

        // 生成新的tokens
        const tokenPayload = {
            userId: user.id,
            username: user.username,
            role: user.role
        };

        const newAccessToken = generateToken(tokenPayload);
        const newRefreshToken = generateRefreshToken(tokenPayload);

        // 更新会话
        await dbConnection.run(
            'UPDATE user_sessions SET session_token = ?, refresh_token = ?, expires_at = datetime("now", "+24 hours") WHERE id = ?',
            [newAccessToken, newRefreshToken, session.id]
        );

        // 记录token刷新
        await logAudit(
            user.id, 
            `Token刷新: ${user.username}`, 
            null, null, null, null, 
            req.ip, 
            req.get('User-Agent')
        );

        sendSuccess(res, {
            user: {
                id: user.id,
                username: user.username,
                fullName: user.full_name,
                role: user.role
            },
            tokens: {
                accessToken: newAccessToken,
                refreshToken: newRefreshToken,
                expiresIn: '24h'
            }
        }, 'Token刷新成功');

    } catch (error) {
        if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
            throw new AuthenticationError('刷新token无效或已过期');
        }
        throw error;
    }
}));

/**
 * 获取当前用户信息
 * GET /api/auth/me
 */
router.get('/me', asyncHandler(async (req, res) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        throw new AuthenticationError('需要提供认证token');
    }

    try {
        const decoded = verifyToken(token);
        
        // 获取用户详细信息
        const user = await dbConnection.queryOne(`
            SELECT id, username, full_name, email, phone, department, position, role, status, last_login, created_at
            FROM users 
            WHERE id = ? AND status = ?
        `, [decoded.userId, 'active']);

        if (!user) {
            throw new AuthenticationError('用户不存在或已被禁用');
        }

        sendSuccess(res, {
            id: user.id,
            username: user.username,
            fullName: user.full_name,
            email: user.email,
            phone: user.phone,
            department: user.department,
            position: user.position,
            role: user.role,
            status: user.status,
            lastLogin: user.last_login,
            createdAt: user.created_at
        }, '获取用户信息成功');

    } catch (error) {
        if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
            throw new AuthenticationError('Token无效或已过期');
        }
        throw error;
    }
}));

/**
 * 修改密码
 * POST /api/auth/change-password
 */
router.post('/change-password', [
    body('currentPassword')
        .notEmpty()
        .withMessage('当前密码不能为空'),
    body('newPassword')
        .isLength({ min: 6 })
        .withMessage('新密码长度至少6个字符')
        .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
        .withMessage('新密码必须包含字母和数字'),
    body('confirmPassword')
        .custom((value, { req }) => {
            if (value !== req.body.newPassword) {
                throw new Error('确认密码与新密码不匹配');
            }
            return true;
        })
], asyncHandler(async (req, res) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        throw handleValidationErrors(errors.array());
    }

    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        throw new AuthenticationError('需要提供认证token');
    }

    const { currentPassword, newPassword } = req.body;

    try {
        const decoded = verifyToken(token);
        
        // 获取用户信息
        const user = await dbConnection.queryOne(
            'SELECT id, username, password_hash FROM users WHERE id = ? AND status = ?',
            [decoded.userId, 'active']
        );

        if (!user) {
            throw new AuthenticationError('用户不存在或已被禁用');
        }

        // 验证当前密码
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
        if (!isCurrentPasswordValid) {
            throw new AuthenticationError('当前密码错误');
        }

        // 加密新密码
        const hashedNewPassword = await bcrypt.hash(newPassword, 10);

        // 更新密码
        await dbConnection.run(
            'UPDATE users SET password_hash = ?, updated_at = datetime("now") WHERE id = ?',
            [hashedNewPassword, user.id]
        );

        // 记录密码修改
        await logAudit(
            user.id, 
            `密码修改: ${user.username}`, 
            'users', 
            user.id, 
            null, 
            null, 
            req.ip, 
            req.get('User-Agent')
        );

        sendSuccess(res, null, '密码修改成功');

    } catch (error) {
        if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
            throw new AuthenticationError('Token无效或已过期');
        }
        throw error;
    }
}));

/**
 * 用户注册
 * POST /api/auth/register
 */
router.post('/register', [
    body('username')
        .isLength({ min: 3, max: 50 })
        .withMessage('用户名长度必须在3-50个字符之间')
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('用户名只能包含字母、数字和下划线'),
    body('email')
        .isEmail()
        .withMessage('请输入有效的邮箱地址'),
    body('full_name')
        .isLength({ min: 2, max: 100 })
        .withMessage('姓名长度必须在2-100个字符之间'),
    body('password')
        .isLength({ min: 6 })
        .withMessage('密码长度至少6个字符')
        .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
        .withMessage('密码必须包含字母和数字'),
    body('department')
        .optional()
        .isLength({ max: 100 })
        .withMessage('部门名称不能超过100个字符'),
    body('position')
        .optional()
        .isLength({ max: 100 })
        .withMessage('职位名称不能超过100个字符')
], asyncHandler(async (req, res) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        throw new ValidationError('输入验证失败', errors.array());
    }

    const { username, email, full_name, password, department, position } = req.body;

    // 检查用户名是否已存在
    const existingUser = await dbConnection.queryOne(
        'SELECT id FROM users WHERE username = ? OR email = ?',
        [username, email]
    );

    if (existingUser) {
        throw new ValidationError('用户名或邮箱已存在');
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12);

    // 创建用户
    const result = await dbConnection.run(`
        INSERT INTO users (username, email, full_name, password, department, position, role, status, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `, [username, email, full_name, hashedPassword, department || null, position || null, 'user', 'active']);

    // 记录审计日志
    await auditLog(
        result.lastID,
        'user_register',
        'users',
        result.lastID,
        { username, email, full_name, department, position },
        req
    );

    sendSuccess(res, {
        id: result.lastID,
        username,
        email,
        fullName: full_name,
        department,
        position,
        role: 'user',
        status: 'active'
    }, '用户注册成功');
}));

/**
 * 数据库初始化端点
 * POST /api/auth/init-db
 */
router.post('/init-db', asyncHandler(async (req, res) => {
    try {
        // 执行数据库初始化（不关闭连接）
        const { createTables, insertDefaultData } = require('../utils/init_db');

        console.log('========================================');
        console.log('🔧 开始初始化数据库');
        console.log('========================================');

        // 创建表
        await createTables();

        // 插入默认数据
        await insertDefaultData();

        // 获取数据库统计信息
        const stats = await dbConnection.getDatabaseStats();

        console.log('========================================');
        console.log('🎉 数据库初始化完成！');
        console.log('========================================');
        console.log(`📊 数据库统计:`);
        console.log(`   - 总表数: ${stats.tables.length}`);
        console.log(`   - 总记录数: ${stats.totalRecords}`);
        console.log('');
        console.log('📋 表详情:');
        stats.tables.forEach(table => {
            console.log(`   - ${table.name}: ${table.records} 条记录`);
        });
        console.log('');
        console.log('👤 默认账号:');
        console.log('   - 管理员: admin / admin123');
        console.log('   - 测试用户: yangjun / 123456');
        console.log('========================================');

        sendSuccess(res, {
            tables: stats.tables.length,
            totalRecords: stats.totalRecords,
            tableDetails: stats.tables
        }, '数据库初始化成功');

    } catch (error) {
        console.error('数据库初始化失败:', error);
        throw new ValidationError('数据库初始化失败: ' + error.message);
    }
}));

module.exports = router;
