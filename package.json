{"name": "comprehensive-management-system", "version": "1.0.0", "description": "企业级综合管理系统 - 基于韵通网络管理模式", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node backend/utils/init_db.js", "test": "npm run init-db && npm start", "install-deps": "npm install", "setup": "npm install && npm run init-db", "frontend": "cd frontend && python -m http.server 8080", "backend": "node server.js", "both": "concurrently \"npm run backend\" \"npm run frontend\""}, "keywords": ["management-system", "enterprise", "work-log", "project-management", "statistics", "nodejs", "express", "sqlite"], "author": "Augment Agent", "license": "MIT", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "uuid": "^9.0.1", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "."}, "bugs": {"url": "."}, "homepage": "."}