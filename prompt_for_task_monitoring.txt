Requirement:

生成一个查询每个工作日志的界面，每个人的统计，是否按时录入信息，漏报日志记录，可以根据新增的xml配置文件增加新的统计功能

File structure in the folder `./task_output`:

```
message_dump.json # a dump of all messages, including non-report ones
2025-07-03_yangjun.xlsx # created by converting the individual daily reports in message_dump.json into table using LLM (Google Gemini)
2025-07-03_yangjun.txt # actually a csv file, converted from xlsx
2025-07-03_yangjun.md # a markdown file generated by LLM
2025-07-03_yangjun.raw.txt # the raw daily report extracted from message_dump.json
texts.db # a sqlite database storing all above files
```

The content in `message_dump.json`:

```json
[
    {
        "timestamp": "1751591337",
        "message": "2025.7.3 工作日志\n1，联志机房房屋协调开发票事宜，禾盛电费联系物业开发票事宜！\n2，采购的交警项目交换机到公司，领取设备并配置数据！",
        "actorId": "yangjun"
    }
]
```

All daily reports must contain the keyword "工作日志", otherwise will be filtered.

The schema of `texts.db`:
```sql
CREATE TABLE IF NOT EXISTS documents (filename TEXT, content TEXT)
```

The content of `2025-07-03_yangjun.txt` (Notice, the schema of the excel is undetermined, so instead of sticking to the schema in the example, just show the entire table as is. You can use the `.md` file to show the table on the web):

```
,日期 ,工作内容 
0,2025.7.3 ,联志机房房屋协调开发票事宜，禾盛电费联系物业开发票事宜！ 
1,2025.7.3 ,采购的交警项目交换机到公司，领取设备并配置数据！ 
```

The content of `2025-07-03_liujiang.md`:

```markdown
| 日期       | 故障地点   | 故障描述       | 处理方法                               | 处理结果   |
|------------|------------|----------------|----------------------------------------|------------|
| 2025年7月03日 | 航天逸居   | 用户电话称无法上网，WAN口不亮 | 现场查看线路，更换网线，接线             | 网络恢复正常 |
| 2025年7月03日 | 文景小区   | 网线被剪，设备损坏           | 更换网线，重找光猫替换                   | 网络恢复正常 |
| 2025年7月03日 | 都市绿洲   | 用户无法正常使用网络         | 重启设备，现场恢复设备，重新调路由器       | 网络恢复正常 |
```

Fill in the blanks below in the code. Make some adjustments according to the requirement, such as adding/removing/replacing some functions, fixing some bugs, etc.

```python
import streamlit # to display markdown, pie-chart, statistics
# optionally, use seaborn to render the stats and show png on streamlit
import os
from collections import defaultdict
import datetime

basedir = "task_output"

# for streamlit
server_port = 19472
server_host = "0.0.0.0"

def compute_report_completion_rate(date_with_report:list, date_without_report:list):
    with_report_days = len(date_with_report)
    without_report_days = len(date_without_report)
    total_days = with_report_days + without_report_days
    ret = with_report_days / total_days
    return ret

def get_dates_with_and_without_report_per_user(records_per_user:dict):
    ...

def read_daily_report_status():
    files = os.listdir(basedir)
    excel_files = [it for it in files if it.endswith(".raw.txt")]
    report_records = [it.split(".")[0] for it in excel_files]
    records_per_user = defaultdict(set)

    min_date, max_date = datetime.datetime.now(), datetime.datetime.fromtimestamp(0)

    for it in report_records:
        date, user = it.split("_")
        date_obj = datetime.datetime.fromisoformat(date)
        records_per_user[user].add(date_obj)
        if min_date>date_obj: min_date = date_obj
        if max_date<date_obj: max_date = date_obj
    return dict(records_per_user=records_per_user, min_date=min_date, max_date=max_date)

def main():
    # serve the status to streamlit
    ...

if __name__ == "__main__":
    main()

```
