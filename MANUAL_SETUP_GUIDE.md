# 🔧 综合管理系统手动配置指南

## 🚨 当前问题诊断

**问题**: 终端环境无法正常执行Python命令（错误代码49）
**原因**: Windows Store Python版本或PATH配置问题
**解决方案**: 手动重新配置Python环境

## 📋 完整配置步骤

### 步骤1: 卸载问题Python版本

1. **打开Windows设置**
   - 按 `Win + I` 打开设置
   - 点击"应用"

2. **卸载Python相关程序**
   - 搜索"Python"
   - 卸载所有Python相关程序（特别是Microsoft Store版本）
   - 重启计算机

### 步骤2: 安装官方Python

1. **下载Python**
   - 访问: https://www.python.org/downloads/
   - 下载Python 3.10或更高版本（推荐3.11.x）

2. **安装Python**
   - 运行下载的安装程序
   - ✅ **重要**: 勾选 "Add Python to PATH"
   - ✅ **重要**: 勾选 "Install for all users"
   - 选择 "Customize installation"
   - 确保以下选项被勾选：
     - pip
     - tcl/tk and IDLE
     - Python test suite
     - py launcher

3. **验证安装**
   - 打开新的命令提示符 (Win + R, 输入 cmd)
   - 运行: `python --version`
   - 运行: `pip --version`
   - 应该显示版本信息

### 步骤3: 安装依赖包

在命令提示符中运行以下命令：

```cmd
# 更新pip
python -m pip install --upgrade pip

# 安装必需包
pip install streamlit
pip install pandas
pip install plotly

# 验证安装
python -c "import streamlit, pandas, plotly; print('所有包安装成功')"
```

### 步骤4: 初始化数据库

```cmd
# 切换到项目目录
cd y:\aicode\airizhi

# 运行数据库初始化
python backend\utils\init_db.py
```

### 步骤5: 启动系统

**方法1: 使用一键启动脚本**
```cmd
python run.py
```

**方法2: 手动启动两个服务**

打开两个命令提示符窗口：

**窗口1 - 启动后端服务:**
```cmd
cd y:\aicode\airizhi
streamlit run backend\app.py --server.port=8501
```

**窗口2 - 启动前端服务:**
```cmd
cd y:\aicode\airizhi\frontend
python -m http.server 8080
```

### 步骤6: 验证系统

1. **访问前端界面**
   - 打开浏览器访问: http://localhost:8080
   - 使用账号登录: admin / admin123

2. **访问后端管理界面**
   - 打开浏览器访问: http://localhost:8501
   - 查看Streamlit管理界面

## 🔍 故障排除

### 问题1: Python命令不识别
**解决方案**:
- 确保Python已添加到PATH
- 重启命令提示符
- 使用完整路径: `C:\Python311\python.exe`

### 问题2: pip安装失败
**解决方案**:
```cmd
# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple streamlit pandas plotly

# 或者升级pip
python -m pip install --upgrade pip
```

### 问题3: 端口被占用
**解决方案**:
```cmd
# 查看端口占用
netstat -ano | findstr 8501
netstat -ano | findstr 8080

# 结束占用进程
taskkill /PID [进程ID] /F
```

### 问题4: 数据库初始化失败
**解决方案**:
- 检查文件权限
- 确保database目录存在
- 手动创建database目录: `mkdir database`

## 📱 系统功能验证清单

完成配置后，请验证以下功能：

### 前端功能 (http://localhost:8080)
- [ ] 页面正常加载
- [ ] 登录功能正常
- [ ] 界面响应正常
- [ ] 导航功能正常

### 后端功能 (http://localhost:8501)
- [ ] Streamlit界面加载
- [ ] 数据库连接正常
- [ ] 用户管理功能
- [ ] 工作日志功能
- [ ] 项目管理功能
- [ ] 统计分析功能

## 🎯 默认账号信息

| 用户类型 | 用户名 | 密码 | 权限 |
|---------|--------|------|------|
| 管理员 | admin | admin123 | 全部权限 |
| 测试用户 | yangjun | 123456 | 普通用户 |

## 📞 技术支持

### 相关文件
- `fix_python_env.ps1` - PowerShell修复脚本
- `start_system.bat` - Windows启动脚本
- `run.py` - Python一键启动脚本
- `docs/DEPLOYMENT.md` - 详细部署文档

### 备用方案
如果仍然无法启动完整系统，可以使用：
- `standalone_demo.html` - 完整前端演示
- `demo.html` - 基础功能展示

## 🏆 成功标准

系统配置成功的标志：
1. ✅ Python命令正常执行
2. ✅ 所有依赖包安装成功
3. ✅ 数据库初始化完成
4. ✅ 前端服务正常运行 (端口8080)
5. ✅ 后端服务正常运行 (端口8501)
6. ✅ 用户可以正常登录和使用功能

---

**注意**: 如果按照此指南操作后仍有问题，请提供具体的错误信息以便进一步诊断。
