# 综合管理系统手动部署指南

## 🔧 环境问题诊断

当前系统检测到Python环境配置问题。请按以下步骤手动部署：

## 📋 部署前准备

### 1. Python环境检查
```bash
# 检查Python版本（需要3.10+）
python --version
# 或
python3 --version

# 如果没有Python，请从官网下载安装：
# https://www.python.org/downloads/
```

### 2. 安装依赖包
```bash
# 安装核心依赖
pip install streamlit pandas plotly

# 或使用requirements.txt
pip install -r config/requirements.txt
```

## 🚀 手动部署步骤

### 步骤1: 初始化数据库
```bash
# 进入项目目录
cd y:\aicode\airizhi

# 运行数据库初始化
python backend/utils/init_db.py
```

### 步骤2: 启动后端服务
```bash
# 启动Streamlit后端（端口8501）
streamlit run backend/app.py --server.port=8501 --server.address=localhost
```

### 步骤3: 启动前端服务（新终端窗口）
```bash
# 进入前端目录
cd y:\aicode\airizhi/frontend

# 启动HTTP服务器（端口8080）
python -m http.server 8080
```

## 🌐 访问系统

- **前端用户界面**: http://localhost:8080
- **后端管理界面**: http://localhost:8501

## 👤 默认登录账号

| 用户类型 | 用户名 | 密码 |
|---------|--------|------|
| 管理员 | admin | admin123 |
| 测试用户 | yangjun | 123456 |

## 🔍 功能验证清单

### ✅ 基础功能测试
- [ ] 前端界面正常加载（http://localhost:8080）
- [ ] 后端界面正常加载（http://localhost:8501）
- [ ] 数据库初始化成功
- [ ] 用户登录功能正常

### ✅ 核心功能测试
- [ ] 工作日志添加功能
- [ ] 工作日志查询功能
- [ ] 特殊项目管理功能
- [ ] 统计分析功能
- [ ] 用户管理功能

## 🐛 常见问题解决

### 问题1: 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :8501
netstat -ano | findstr :8080

# 杀死占用进程
taskkill /PID <进程ID> /F
```

### 问题2: 模块导入错误
```bash
# 确保在项目根目录
cd y:\aicode\airizhi

# 设置Python路径
set PYTHONPATH=%PYTHONPATH%;%CD%
```

### 问题3: 数据库权限问题
```bash
# 检查database目录权限
ls -la database/

# 如果需要，创建数据库目录
mkdir -p database
```

## 📱 使用指南

### 前端界面使用
1. 访问 http://localhost:8080
2. 使用默认账号登录
3. 体验iOS风格的用户界面

### 后端管理界面使用
1. 访问 http://localhost:8501
2. 直接进入Streamlit管理界面
3. 使用完整的管理功能

## 🔄 一键启动脚本

如果Python环境正常，可以使用：
```bash
python run.py
```

## 📞 技术支持

如遇到问题：
1. 检查Python版本是否为3.10+
2. 确认所有依赖包已正确安装
3. 验证端口8501和8080未被占用
4. 检查防火墙设置

## 🎯 成功标准

部署成功的标志：
- ✅ 两个服务都正常启动
- ✅ 前端界面可以正常访问
- ✅ 后端管理界面可以正常访问
- ✅ 用户可以成功登录
- ✅ 核心功能正常运行

---

**注意**: 如果遇到Python环境问题，建议重新安装Python 3.10+版本，并确保添加到系统PATH中。
