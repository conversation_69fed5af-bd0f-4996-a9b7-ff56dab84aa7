@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 综合管理系统启动器
echo ========================================
echo.

:: 检查Python环境
echo [1/5] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Python环境未配置
    echo 请先运行 fix_python_env.bat 修复Python环境
    pause
    exit /b 1
)

echo ✓ Python环境正常
python --version
echo.

:: 检查依赖包
echo [2/5] 检查依赖包...
python -c "import streamlit, pandas, plotly" >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ 依赖包未安装
    echo 正在安装依赖包...
    pip install streamlit pandas plotly
    if !errorlevel! neq 0 (
        echo ✗ 依赖包安装失败
        pause
        exit /b 1
    )
)

echo ✓ 依赖包检查通过
echo.

:: 初始化数据库
echo [3/5] 初始化数据库...
if not exist "database\management_system.db" (
    echo 正在创建数据库...
    python backend\utils\init_db.py
    if !errorlevel! equ 0 (
        echo ✓ 数据库初始化成功
    ) else (
        echo ✗ 数据库初始化失败
        pause
        exit /b 1
    )
) else (
    echo ✓ 数据库已存在
)

echo.

:: 检查端口占用
echo [4/5] 检查端口占用...
netstat -an | find "8501" >nul
if %errorlevel% equ 0 (
    echo ⚠ 端口8501已被占用，正在尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| find "8501"') do (
        taskkill /PID %%a /F >nul 2>&1
    )
)

netstat -an | find "8080" >nul
if %errorlevel% equ 0 (
    echo ⚠ 端口8080已被占用，正在尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| find "8080"') do (
        taskkill /PID %%a /F >nul 2>&1
    )
)

echo ✓ 端口检查完成
echo.

:: 启动服务
echo [5/5] 启动服务...
echo.
echo 正在启动后端服务 (端口8501)...
start "Streamlit Backend" cmd /k "cd /d %~dp0 && streamlit run backend\app.py --server.port=8501"

echo 等待后端服务启动...
timeout /t 5 /nobreak >nul

echo 正在启动前端服务 (端口8080)...
start "Frontend Server" cmd /k "cd /d %~dp0\frontend && python -m http.server 8080"

echo 等待前端服务启动...
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo 🎉 系统启动完成！
echo ========================================
echo.
echo 访问地址:
echo 📱 前端用户界面: http://localhost:8080
echo 🔧 后端管理界面: http://localhost:8501
echo.
echo 默认账号:
echo 👤 管理员: admin / admin123
echo 👤 测试用户: yangjun / 123456
echo.
echo 按任意键打开浏览器访问前端界面...
pause >nul

start http://localhost:8080

echo.
echo 系统正在运行中...
echo 关闭此窗口将停止所有服务
echo.
pause
