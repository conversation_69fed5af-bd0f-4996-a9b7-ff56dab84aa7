#!/usr/bin/env python3
"""
数据库初始化脚本
"""

import sqlite3
import os
import sys
from pathlib import Path
from datetime import datetime, date
import hashlib

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

def get_db_path():
    """获取数据库文件路径"""
    db_dir = project_root / "database"
    db_dir.mkdir(exist_ok=True)
    return db_dir / "comprehensive_management.db"

def hash_password(password: str) -> str:
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def init_database():
    """初始化数据库"""
    db_path = get_db_path()
    
    # 如果数据库已存在，询问是否重新创建
    if db_path.exists():
        response = input(f"数据库文件 {db_path} 已存在，是否重新创建？(y/N): ")
        if response.lower() != 'y':
            print("取消初始化")
            return
        os.remove(db_path)
    
    # 读取SQL初始化脚本
    sql_file = project_root / "database" / "init.sql"
    if not sql_file.exists():
        print(f"错误：找不到SQL初始化文件 {sql_file}")
        return
    
    with open(sql_file, 'r', encoding='utf-8') as f:
        sql_script = f.read()
    
    # 创建数据库连接并执行初始化脚本
    conn = sqlite3.connect(db_path)
    try:
        conn.executescript(sql_script)
        print("数据库表结构创建成功")
        
        # 插入初始数据
        insert_initial_data(conn)
        
        conn.commit()
        print(f"数据库初始化完成：{db_path}")
        
    except Exception as e:
        print(f"数据库初始化失败：{e}")
        conn.rollback()
    finally:
        conn.close()

def insert_initial_data(conn):
    """插入初始数据"""
    cursor = conn.cursor()
    
    # 插入部门数据
    departments = [
        ("综合管理部", "负责公司综合管理事务"),
        ("网管中心", "负责网络管理和维护"),
        ("财务资产部", "负责财务管理和资产管理"),
        ("航天分公司", "航天业务分公司"),
        ("经营一部", "经营业务第一部"),
        ("经营二部", "经营业务第二部"),
        ("经营三部", "经营业务第三部"),
        ("经营管理中心", "经营管理中心"),
        ("文化事业部", "文化事业部"),
        ("行业事业部", "行业事业部"),
    ]
    
    cursor.executemany(
        "INSERT INTO departments (name, description) VALUES (?, ?)",
        departments
    )
    
    # 插入管理员用户
    admin_password = hash_password("admin123")
    cursor.execute("""
        INSERT INTO users (username, email, password_hash, full_name, department, position, is_admin)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """, ("admin", "<EMAIL>", admin_password, "系统管理员", "综合管理部", "系统管理员", 1))
    
    # 插入测试用户
    test_users = [
        ("yangjun", "<EMAIL>", hash_password("123456"), "杨军", "网管中心", "网络工程师", 0),
        ("liujiang", "<EMAIL>", hash_password("123456"), "刘江", "网管中心", "技术支持", 0),
        ("zhangsimo", "<EMAIL>", hash_password("123456"), "张思默", "财务资产部", "财务专员", 0),
    ]
    
    cursor.executemany("""
        INSERT INTO users (username, email, password_hash, full_name, department, position, is_admin)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """, test_users)
    
    # 插入示例工作日志
    sample_logs = [
        (2, "2025-07-03", "联志机房房屋协调开发票事宜，禾盛电费联系物业开发票事宜！\n采购的交警项目交换机到公司，领取设备并配置数据！", "日常工作"),
        (3, "2025-07-03", "航天逸居用户电话称无法上网，WAN口不亮，现场查看线路，更换网线，接线，网络恢复正常\n文景小区网线被剪，设备损坏，更换网线，重找光猫替换，网络恢复正常", "故障处理"),
    ]
    
    cursor.executemany("""
        INSERT INTO work_logs (user_id, log_date, content, category)
        VALUES (?, ?, ?, ?)
    """, sample_logs)
    
    # 插入示例特殊项目数据
    sample_projects = [
        ("2025-07", "航天逸居网络项目", "电信", 100, "月付", 5000.00, 500.00, 4500.00, 10.00, 1),
        ("2025-07", "文景小区宽带项目", "联通", 200, "年付", 12000.00, 1200.00, 10800.00, 10.00, 1),
    ]
    
    cursor.executemany("""
        INSERT INTO special_projects (month, project_name, operator, bandwidth_mbps, payment_method, 
                                    received_amount, business_fee, actual_received, business_fee_ratio, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, sample_projects)
    
    # 插入系统配置
    system_configs = [
        ("app_name", "综合管理系统", "string", "应用程序名称"),
        ("app_version", "1.0.0", "string", "应用程序版本"),
        ("log_retention_days", "90", "number", "日志保留天数"),
        ("max_file_size_mb", "10", "number", "最大文件上传大小(MB)"),
        ("enable_email_notifications", "true", "boolean", "是否启用邮件通知"),
    ]
    
    cursor.executemany("""
        INSERT INTO system_configs (config_key, config_value, config_type, description)
        VALUES (?, ?, ?, ?)
    """, system_configs)
    
    # 插入示例统计配置
    sample_xml_config = """<?xml version="1.0" encoding="UTF-8"?>
<statistics>
    <config name="daily_completion_rate">
        <description>每日完成率统计</description>
        <fields>
            <field name="user_id" type="integer" required="true"/>
            <field name="completion_rate" type="decimal" calculation="completed_tasks/total_tasks"/>
        </fields>
        <filters>
            <filter name="date_range" type="date_range"/>
            <filter name="department" type="string"/>
        </filters>
    </config>
</statistics>"""
    
    cursor.execute("""
        INSERT INTO log_stat_configs (name, description, config_xml, created_by)
        VALUES (?, ?, ?, ?)
    """, ("每日完成率统计", "统计用户每日工作完成率", sample_xml_config, 1))
    
    print("初始数据插入成功")

if __name__ == "__main__":
    init_database()
