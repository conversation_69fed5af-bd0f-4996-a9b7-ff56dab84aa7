"""
工作日志服务
"""

from typing import List, Optional
from datetime import date, datetime, timedelta
from backend.models.work_log import WorkLog
from backend.models.user import User

class WorkLogService:
    """工作日志服务类"""
    
    def add_log(self, user_id: int, log_date: date, content: str, category: str = None) -> bool:
        """添加工作日志"""
        # 检查是否已存在该日期的日志
        existing_log = WorkLog.find_by_user_and_date(user_id, log_date)
        if existing_log:
            return False
        
        # 创建新日志
        work_log = WorkLog()
        work_log.user_id = user_id
        work_log.log_date = log_date
        work_log.content = content.strip()
        work_log.category = category
        work_log.status = 'submitted'
        
        return work_log.save()
    
    def update_log(self, log_id: int, content: str = None, category: str = None, 
                   status: str = None) -> bool:
        """更新工作日志"""
        work_log = WorkLog.find_by_id(log_id)
        if not work_log:
            return False
        
        if content is not None:
            work_log.content = content.strip()
        if category is not None:
            work_log.category = category
        if status is not None:
            work_log.status = status
        
        return work_log.save()
    
    def get_logs_by_user(self, user_id: int, limit: int = None) -> List[WorkLog]:
        """获取用户的工作日志"""
        return WorkLog.find_by_user(user_id, limit)
    
    def get_logs_by_date_range(self, start_date: date, end_date: date, 
                              username: str = None) -> List[WorkLog]:
        """根据日期范围获取工作日志"""
        if username and username != "全部":
            user = User.find_by_username(username)
            if user:
                return WorkLog.find_by_date_range(start_date, end_date, user.id)
            else:
                return []
        else:
            return WorkLog.find_by_date_range(start_date, end_date)
    
    def get_missing_logs(self, user_id: int, start_date: date, end_date: date) -> List[date]:
        """获取用户缺失的工作日志日期"""
        return WorkLog.get_missing_logs(user_id, start_date, end_date)
    
    def get_user_completion_rate(self, user_id: int, start_date: date, end_date: date) -> float:
        """计算用户在指定日期范围内的日志完成率"""
        # 计算工作日总数
        total_workdays = 0
        current_date = start_date
        
        while current_date <= end_date:
            if current_date.weekday() < 5:  # 周一到周五
                total_workdays += 1
            current_date = date.fromordinal(current_date.toordinal() + 1)
        
        if total_workdays == 0:
            return 0.0
        
        # 计算已提交的日志数量
        submitted_logs = len(WorkLog.find_by_date_range(start_date, end_date, user_id))
        
        return (submitted_logs / total_workdays) * 100
    
    def get_daily_log_stats(self, target_date: date) -> dict:
        """获取指定日期的日志统计"""
        logs = WorkLog.find_all("log_date = ?", (target_date,))
        
        # 按分类统计
        category_stats = {}
        for log in logs:
            category = log.category or '未分类'
            category_stats[category] = category_stats.get(category, 0) + 1
        
        # 按用户统计
        user_stats = {}
        for log in logs:
            user = log.get_user()
            if user:
                user_stats[user.username] = user_stats.get(user.username, 0) + 1
        
        return {
            'date': target_date.strftime('%Y-%m-%d'),
            'total_logs': len(logs),
            'category_stats': category_stats,
            'user_stats': user_stats
        }
    
    def search_logs(self, keyword: str, user_id: int = None, 
                   start_date: date = None, end_date: date = None) -> List[WorkLog]:
        """搜索工作日志"""
        where_conditions = ["content LIKE ?"]
        params = [f"%{keyword}%"]
        
        if user_id:
            where_conditions.append("user_id = ?")
            params.append(user_id)
        
        if start_date:
            where_conditions.append("log_date >= ?")
            params.append(start_date)
        
        if end_date:
            where_conditions.append("log_date <= ?")
            params.append(end_date)
        
        where_clause = " AND ".join(where_conditions) + " ORDER BY log_date DESC"
        
        return WorkLog.find_all(where_clause, tuple(params))
    
    def export_logs_to_dict(self, logs: List[WorkLog]) -> List[dict]:
        """将日志列表导出为字典格式"""
        result = []
        
        for log in logs:
            user = log.get_user()
            log_dict = log.to_dict()
            log_dict['username'] = user.username if user else '未知用户'
            log_dict['full_name'] = user.full_name if user else '未知用户'
            log_dict['department'] = user.department if user else '未知部门'
            result.append(log_dict)
        
        return result
    
    def get_recent_activity(self, days: int = 7) -> List[dict]:
        """获取最近几天的活动统计"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        activity = []
        current_date = start_date
        
        while current_date <= end_date:
            stats = self.get_daily_log_stats(current_date)
            activity.append(stats)
            current_date = date.fromordinal(current_date.toordinal() + 1)
        
        return activity
