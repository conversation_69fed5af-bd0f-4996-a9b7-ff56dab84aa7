@echo off
echo ========================================
echo 综合管理系统 - 前端演示启动器
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Python未安装或未添加到PATH
    echo.
    echo 解决方案:
    echo 1. 从 https://www.python.org/downloads/ 下载并安装Python 3.10+
    echo 2. 安装时勾选 "Add Python to PATH"
    echo 3. 重新运行此脚本
    echo.
    echo 或者直接打开 frontend/index.html 查看静态演示
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在启动前端演示服务器...
echo 访问地址: http://localhost:8080
echo 按 Ctrl+C 停止服务
echo.

cd frontend
python -m http.server 8080

pause
