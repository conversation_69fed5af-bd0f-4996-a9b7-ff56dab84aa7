"""
基础数据模型
"""

import sqlite3
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime
import json

class DatabaseManager:
    """数据库管理器"""
    
    _instance = None
    _db_path = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._db_path is None:
            project_root = Path(__file__).parent.parent.parent
            self._db_path = project_root / "database" / "comprehensive_management.db"
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self._db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
        return conn
    
    def execute_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """执行查询"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """执行更新操作"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.rowcount
    
    def execute_insert(self, query: str, params: tuple = ()) -> int:
        """执行插入操作，返回新记录的ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.lastrowid

class BaseModel:
    """基础模型类"""
    
    table_name = None
    
    def __init__(self, **kwargs):
        self.db = DatabaseManager()
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    @classmethod
    def find_by_id(cls, id: int) -> Optional['BaseModel']:
        """根据ID查找记录"""
        db = DatabaseManager()
        query = f"SELECT * FROM {cls.table_name} WHERE id = ?"
        rows = db.execute_query(query, (id,))
        if rows:
            return cls(**dict(rows[0]))
        return None
    
    @classmethod
    def find_all(cls, where: str = "", params: tuple = ()) -> List['BaseModel']:
        """查找所有记录"""
        db = DatabaseManager()
        query = f"SELECT * FROM {cls.table_name}"
        if where:
            query += f" WHERE {where}"
        rows = db.execute_query(query, params)
        return [cls(**dict(row)) for row in rows]
    
    @classmethod
    def count(cls, where: str = "", params: tuple = ()) -> int:
        """统计记录数量"""
        db = DatabaseManager()
        query = f"SELECT COUNT(*) as count FROM {cls.table_name}"
        if where:
            query += f" WHERE {where}"
        rows = db.execute_query(query, params)
        return rows[0]['count'] if rows else 0
    
    def save(self) -> bool:
        """保存记录"""
        if hasattr(self, 'id') and self.id:
            return self._update()
        else:
            return self._insert()
    
    def _insert(self) -> bool:
        """插入新记录"""
        # 子类需要实现具体的插入逻辑
        raise NotImplementedError
    
    def _update(self) -> bool:
        """更新记录"""
        # 子类需要实现具体的更新逻辑
        raise NotImplementedError
    
    def delete(self) -> bool:
        """删除记录"""
        if not hasattr(self, 'id') or not self.id:
            return False
        
        query = f"DELETE FROM {self.table_name} WHERE id = ?"
        rowcount = self.db.execute_update(query, (self.id,))
        return rowcount > 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if not key.startswith('_') and key != 'db':
                if isinstance(value, datetime):
                    result[key] = value.isoformat()
                else:
                    result[key] = value
        return result
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
