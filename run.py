#!/usr/bin/env python3
"""
综合管理系统启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_requirements():
    """检查依赖是否安装"""
    try:
        import streamlit
        import pandas
        import plotly
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r config/requirements.txt")
        return False

def init_database():
    """初始化数据库"""
    try:
        from backend.utils.init_db import init_database
        print("正在初始化数据库...")
        init_database()
        print("✓ 数据库初始化成功")
        return True
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        return False

def start_streamlit():
    """启动Streamlit应用"""
    try:
        app_path = Path(__file__).parent / "backend" / "app.py"
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            str(app_path),
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ]
        
        print("正在启动Streamlit应用...")
        process = subprocess.Popen(cmd, cwd=Path(__file__).parent)
        
        # 等待服务启动
        time.sleep(3)
        
        print("✓ Streamlit应用已启动")
        print("📊 访问地址: http://localhost:8501")
        
        return process
    except Exception as e:
        print(f"✗ 启动Streamlit失败: {e}")
        return None

def start_frontend_server():
    """启动前端服务器（简单的HTTP服务器）"""
    try:
        frontend_path = Path(__file__).parent / "frontend"
        
        # 使用Python内置的HTTP服务器
        cmd = [sys.executable, "-m", "http.server", "8080"]
        
        print("正在启动前端服务器...")
        process = subprocess.Popen(cmd, cwd=frontend_path)
        
        # 等待服务启动
        time.sleep(2)
        
        print("✓ 前端服务器已启动")
        print("🌐 访问地址: http://localhost:8080")
        
        return process
    except Exception as e:
        print(f"✗ 启动前端服务器失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 综合管理系统启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        return
    
    # 初始化数据库
    if not init_database():
        print("⚠️  数据库初始化失败，但系统仍可运行")
    
    # 启动服务
    streamlit_process = start_streamlit()
    frontend_process = start_frontend_server()
    
    if streamlit_process and frontend_process:
        print("\n" + "=" * 50)
        print("🎉 系统启动成功！")
        print("=" * 50)
        print("📊 后端管理界面: http://localhost:8501")
        print("🌐 前端用户界面: http://localhost:8080")
        print("=" * 50)
        print("\n默认账号:")
        print("管理员: admin / admin123")
        print("测试用户: yangjun / 123456")
        print("\n按 Ctrl+C 停止服务")
        
        # 自动打开浏览器
        try:
            webbrowser.open("http://localhost:8080")
        except:
            pass
        
        try:
            # 等待用户中断
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在停止服务...")
            
            if streamlit_process:
                streamlit_process.terminate()
            if frontend_process:
                frontend_process.terminate()
            
            print("✓ 服务已停止")
    else:
        print("✗ 系统启动失败")

if __name__ == "__main__":
    main()
