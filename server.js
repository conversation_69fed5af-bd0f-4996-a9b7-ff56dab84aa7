const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

// 导入数据库连接
const { dbConnection } = require('./backend/database/connection');

// 导入路由
const authRoutes = require('./backend/routes/auth');
const userRoutes = require('./backend/routes/users');
const workLogRoutes = require('./backend/routes/workLogs');
const projectRoutes = require('./backend/routes/projects');
const statisticsRoutes = require('./backend/routes/statistics');

// 导入中间件
const { authenticateToken } = require('./backend/middleware/auth');
const { errorHandler } = require('./backend/middleware/errorHandler');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com", "https://cdnjs.cloudflare.com"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com", "https://cdnjs.cloudflare.com"],
            fontSrc: ["'self'", "https://cdnjs.cloudflare.com"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));

// CORS配置
app.use(cors({
    origin: ['http://localhost:8080', 'http://127.0.0.1:8080'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

// 速率限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 限制每个IP 15分钟内最多100个请求
    message: {
        error: '请求过于频繁，请稍后再试',
        code: 'RATE_LIMIT_EXCEEDED'
    }
});
app.use('/api/', limiter);

// 日志中间件
app.use(morgan('combined'));

// 解析JSON和URL编码数据
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/static', express.static(path.join(__dirname, 'frontend')));

// 健康检查端点
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        service: 'comprehensive-management-system',
        version: '1.0.0'
    });
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/users', authenticateToken, userRoutes);
app.use('/api/work-logs', authenticateToken, workLogRoutes);
app.use('/api/projects', authenticateToken, projectRoutes);
app.use('/api/statistics', authenticateToken, statisticsRoutes);

// 根路径重定向到前端
app.get('/', (req, res) => {
    res.json({
        message: '综合管理系统 API 服务',
        version: '1.0.0',
        endpoints: {
            health: '/health',
            auth: '/api/auth',
            users: '/api/users',
            workLogs: '/api/work-logs',
            projects: '/api/projects',
            statistics: '/api/statistics'
        },
        frontend: 'http://localhost:8080',
        documentation: '/api/docs'
    });
});

// API文档端点
app.get('/api/docs', (req, res) => {
    res.json({
        title: '综合管理系统 API 文档',
        version: '1.0.0',
        baseUrl: `http://localhost:${PORT}/api`,
        endpoints: {
            authentication: {
                login: 'POST /api/auth/login',
                logout: 'POST /api/auth/logout',
                refresh: 'POST /api/auth/refresh'
            },
            users: {
                list: 'GET /api/users',
                create: 'POST /api/users',
                update: 'PUT /api/users/:id',
                delete: 'DELETE /api/users/:id'
            },
            workLogs: {
                list: 'GET /api/work-logs',
                create: 'POST /api/work-logs',
                update: 'PUT /api/work-logs/:id',
                delete: 'DELETE /api/work-logs/:id'
            },
            projects: {
                list: 'GET /api/projects',
                create: 'POST /api/projects',
                update: 'PUT /api/projects/:id',
                delete: 'DELETE /api/projects/:id'
            },
            statistics: {
                overview: 'GET /api/statistics/overview',
                workLogs: 'GET /api/statistics/work-logs',
                projects: 'GET /api/statistics/projects'
            }
        }
    });
});

// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        error: '端点未找到',
        message: `路径 ${req.originalUrl} 不存在`,
        code: 'ENDPOINT_NOT_FOUND'
    });
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器和数据库连接
async function startServer() {
    try {
        // 连接数据库
        await dbConnection.connect();
        console.log('✓ 数据库连接成功');

        // 启动HTTP服务器
        app.listen(PORT, () => {
            console.log('========================================');
            console.log('🚀 综合管理系统后端服务已启动');
            console.log('========================================');
            console.log(`📡 服务地址: http://localhost:${PORT}`);
            console.log(`📊 API文档: http://localhost:${PORT}/api/docs`);
            console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
            console.log(`📱 前端地址: http://localhost:8080`);
            console.log('========================================');
            console.log('💡 提示: 使用 Ctrl+C 停止服务');
            console.log('');
        });
    } catch (error) {
        console.error('❌ 服务器启动失败:', error.message);
        process.exit(1);
    }
}

startServer();

// 优雅关闭
async function gracefulShutdown(signal) {
    console.log(`\n收到${signal}信号，正在优雅关闭服务器...`);
    try {
        await dbConnection.close();
        console.log('✓ 数据库连接已关闭');
        process.exit(0);
    } catch (error) {
        console.error('❌ 关闭数据库连接失败:', error.message);
        process.exit(1);
    }
}

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

module.exports = app;
