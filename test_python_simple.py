#!/usr/bin/env python3
"""
简单的Python环境测试脚本
用于验证Python环境是否正常工作
"""

import sys
import os
import subprocess

def test_python_version():
    """测试Python版本"""
    print("=" * 50)
    print("Python环境测试")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    print()

def test_imports():
    """测试必需包导入"""
    print("测试包导入...")
    packages = ['streamlit', 'pandas', 'plotly']
    
    for package in packages:
        try:
            __import__(package)
            print(f"✓ {package} - 导入成功")
        except ImportError as e:
            print(f"✗ {package} - 导入失败: {e}")
            return False
    
    print("✓ 所有包导入成功")
    return True

def test_database_init():
    """测试数据库初始化"""
    print("\n测试数据库初始化...")
    
    # 检查数据库目录
    db_dir = "database"
    if not os.path.exists(db_dir):
        os.makedirs(db_dir)
        print(f"✓ 创建数据库目录: {db_dir}")
    
    # 检查初始化脚本
    init_script = "backend/utils/init_db.py"
    if os.path.exists(init_script):
        print(f"✓ 找到初始化脚本: {init_script}")
        try:
            # 尝试运行初始化脚本
            result = subprocess.run([sys.executable, init_script], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print("✓ 数据库初始化成功")
                return True
            else:
                print(f"✗ 数据库初始化失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"✗ 数据库初始化异常: {e}")
            return False
    else:
        print(f"✗ 未找到初始化脚本: {init_script}")
        return False

def test_streamlit():
    """测试Streamlit是否可用"""
    print("\n测试Streamlit...")
    try:
        result = subprocess.run([sys.executable, "-m", "streamlit", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ Streamlit版本: {result.stdout.strip()}")
            return True
        else:
            print(f"✗ Streamlit测试失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Streamlit测试异常: {e}")
        return False

def main():
    """主测试函数"""
    test_python_version()
    
    # 测试包导入
    if not test_imports():
        print("\n❌ 包导入测试失败，请安装必需的包:")
        print("pip install streamlit pandas plotly")
        return False
    
    # 测试数据库初始化
    if not test_database_init():
        print("\n❌ 数据库初始化失败")
        return False
    
    # 测试Streamlit
    if not test_streamlit():
        print("\n❌ Streamlit测试失败")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！Python环境配置正确")
    print("=" * 50)
    print("\n下一步：运行以下命令启动系统")
    print("python run.py")
    print("\n或手动启动：")
    print("1. streamlit run backend/app.py --server.port=8501")
    print("2. cd frontend && python -m http.server 8080")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生异常: {e}")
        sys.exit(1)
