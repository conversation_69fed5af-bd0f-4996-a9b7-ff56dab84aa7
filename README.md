# 综合管理系统

## 项目简介

基于韵通网络知识库系统的文档管理模式，结合工作日志管理需求开发的综合管理系统。

## 技术栈

### 后端
- **Python 3.10+**
- **Streamlit** - Web应用框架
- **SQLite** - 数据存储
- **Pandas** - 数据处理
- **XML** - 配置文件支持

### 前端
- **HTML5** - 页面结构
- **Tailwind CSS** - 样式框架
- **Font Awesome** - 图标库
- **JavaScript** - 交互逻辑

### 开发工具
- **VS Code** - 推荐IDE
- **Git** - 版本控制
- **Python venv** - 虚拟环境

## 项目结构

```
comprehensive-management-system/
├── backend/                    # 后端代码
│   ├── app.py                 # Streamlit主应用
│   ├── models/                # 数据模型
│   ├── services/              # 业务逻辑
│   ├── utils/                 # 工具函数
│   └── config/                # 配置文件
├── frontend/                  # 前端代码
│   ├── index.html            # 主页面
│   ├── login.html            # 登录页面
│   ├── register.html         # 注册页面
│   ├── assets/               # 静态资源
│   └── js/                   # JavaScript文件
├── database/                  # 数据库相关
│   ├── init.sql              # 数据库初始化脚本
│   └── migrations/           # 数据库迁移文件
├── docs/                     # 项目文档
├── tests/                    # 测试文件
├── config/                   # 项目配置
│   ├── .editorconfig         # 编辑器配置
│   ├── .gitignore           # Git忽略文件
│   └── requirements.txt      # Python依赖
└── README.md                 # 项目说明
```

## 快速开始

### 环境要求
- Python 3.10+
- Node.js 18+ (可选，用于前端开发)

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd comprehensive-management-system
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. 安装依赖
```bash
pip install -r config/requirements.txt
```

4. 初始化数据库
```bash
python backend/utils/init_db.py
```

5. 启动应用
```bash
streamlit run backend/app.py --server.port 19472 --server.address 0.0.0.0
```

## 功能模块

### 1. 工作日志管理
- 日志录入和查询
- 统计分析和报表
- 漏报检测
- XML配置扩展

### 2. 文档管理
- 多部门分类管理
- 权限控制
- 版本管理
- 文档搜索

### 3. 用户管理
- 用户注册登录
- 权限管理
- 个人信息管理

## 开发规范

### Git分支规范
- `main` - 生产环境分支
- `develop` - 开发主分支
- `feature/xxx` - 功能开发分支
- `fix/xxx` - 问题修复分支
- `release/vx.x.x` - 发布分支

### 提交信息格式
```
<type>: <subject>

类型说明：
- feat: 新增功能
- fix: 修复bug
- docs: 文档修改
- style: 代码格式化
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动
```

## 部署说明

详见 [部署文档](docs/deployment.md)

## 贡献指南

详见 [贡献指南](docs/contributing.md)

## 许可证

MIT License
