/* iOS风格样式 */

/* 全局样式 */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Aria<PERSON>, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* iOS风格按钮 */
.ios-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.ios-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.ios-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

/* iOS风格输入框 */
.ios-input {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.ios-input:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    background: rgba(255, 255, 255, 1);
}

/* iOS风格卡片 */
.ios-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* iOS风格导航 */
.ios-nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* iOS风格列表 */
.ios-list {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.ios-list-item {
    padding: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;
}

.ios-list-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.ios-list-item:last-child {
    border-bottom: none;
}

/* iOS风格开关 */
.ios-switch {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
}

.ios-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ios-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 31px;
}

.ios-slider:before {
    position: absolute;
    content: "";
    height: 25px;
    width: 25px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .ios-slider {
    background-color: #007AFF;
}

input:checked + .ios-slider:before {
    transform: translateX(20px);
}

/* iOS风格进度条 */
.ios-progress {
    width: 100%;
    height: 4px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.ios-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 100%);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* iOS风格模态框 */
.ios-modal {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* iOS风格标签页 */
.ios-tabs {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.ios-tab {
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.ios-tab.active {
    background: #007AFF;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ios-card {
        margin: 16px;
        border-radius: 12px;
    }
    
    .ios-button {
        width: 100%;
        padding: 14px 24px;
    }
    
    .ios-input {
        font-size: 16px; /* 防止iOS缩放 */
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        color: #ffffff;
    }
    
    .ios-card {
        background: rgba(28, 28, 30, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .ios-input {
        background: rgba(28, 28, 30, 0.9);
        color: #ffffff;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .ios-list {
        background: rgba(28, 28, 30, 0.95);
    }
    
    .ios-list-item {
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }
    
    .ios-list-item:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
}
