<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>综合管理系统 - 演示版</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .ios-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .ios-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .ios-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        .feature-card {
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 via-white to-purple-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="ios-card mx-4 mt-4 rounded-2xl p-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <i class="fas fa-chart-line text-white"></i>
                </div>
                <h1 class="text-xl font-bold text-gray-800">综合管理系统</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600">演示版本 v1.0.0</span>
                <button class="ios-button text-white px-4 py-2 rounded-xl text-sm">
                    <i class="fas fa-user mr-2"></i>登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mx-auto px-4 py-8">
        <!-- 系统介绍 -->
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gray-800 mb-4">企业级工作管理平台</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                基于韵通网络管理模式，集成工作日志、项目管理、统计分析的一体化解决方案
            </p>
        </div>

        <!-- 功能特性 -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div class="feature-card ios-card rounded-2xl p-6 text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-clipboard-list text-2xl text-blue-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">工作日志</h3>
                <p class="text-sm text-gray-600">日常工作记录、完成率统计、缺失日志提醒</p>
            </div>

            <div class="feature-card ios-card rounded-2xl p-6 text-center">
                <div class="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-project-diagram text-2xl text-green-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">项目管理</h3>
                <p class="text-sm text-gray-600">特殊项目跟踪、财务数据管理、运营商统计</p>
            </div>

            <div class="feature-card ios-card rounded-2xl p-6 text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-chart-bar text-2xl text-purple-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">统计分析</h3>
                <p class="text-sm text-gray-600">多维度报表、数据可视化、趋势分析</p>
            </div>

            <div class="feature-card ios-card rounded-2xl p-6 text-center">
                <div class="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-2xl text-orange-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">用户管理</h3>
                <p class="text-sm text-gray-600">权限控制、部门管理、操作审计</p>
            </div>
        </div>

        <!-- 技术架构 -->
        <div class="ios-card rounded-2xl p-8 mb-12">
            <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">技术架构</h3>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-20 h-20 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-server text-3xl text-blue-600"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-2">后端技术</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>Python 3.10+</li>
                        <li>Streamlit 框架</li>
                        <li>SQLite 数据库</li>
                        <li>Pandas 数据处理</li>
                    </ul>
                </div>

                <div class="text-center">
                    <div class="w-20 h-20 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-palette text-3xl text-green-600"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-2">前端技术</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>HTML5 + CSS3</li>
                        <li>Tailwind CSS</li>
                        <li>JavaScript ES6+</li>
                        <li>iOS 风格设计</li>
                    </ul>
                </div>

                <div class="text-center">
                    <div class="w-20 h-20 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-cogs text-3xl text-purple-600"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-2">开发工具</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>VS Code</li>
                        <li>Git 版本控制</li>
                        <li>pytest 测试</li>
                        <li>Black 代码格式化</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 部署状态 -->
        <div class="ios-card rounded-2xl p-8 mb-12">
            <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">部署状态</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-yellow-50 rounded-xl">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-exclamation-triangle text-yellow-600"></i>
                        <span class="font-medium text-gray-800">Python 环境</span>
                    </div>
                    <span class="text-yellow-600 font-medium">需要配置</span>
                </div>

                <div class="flex items-center justify-between p-4 bg-green-50 rounded-xl">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-check-circle text-green-600"></i>
                        <span class="font-medium text-gray-800">项目文件</span>
                    </div>
                    <span class="text-green-600 font-medium">已就绪</span>
                </div>

                <div class="flex items-center justify-between p-4 bg-green-50 rounded-xl">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-check-circle text-green-600"></i>
                        <span class="font-medium text-gray-800">数据库设计</span>
                    </div>
                    <span class="text-green-600 font-medium">已完成</span>
                </div>

                <div class="flex items-center justify-between p-4 bg-green-50 rounded-xl">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-check-circle text-green-600"></i>
                        <span class="font-medium text-gray-800">前端界面</span>
                    </div>
                    <span class="text-green-600 font-medium">已完成</span>
                </div>
            </div>
        </div>

        <!-- 部署指南 -->
        <div class="ios-card rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">部署指南</h3>
            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">环境要求</h4>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-check text-green-500"></i>
                            <span>Python 3.10 或更高版本</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-check text-green-500"></i>
                            <span>pip 包管理器</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-check text-green-500"></i>
                            <span>2GB+ 内存</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-check text-green-500"></i>
                            <span>1GB+ 存储空间</span>
                        </li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">快速启动</h4>
                    <div class="bg-gray-100 rounded-xl p-4 text-sm font-mono">
                        <div class="text-gray-600"># 安装依赖</div>
                        <div class="text-blue-600">pip install streamlit pandas plotly</div>
                        <br>
                        <div class="text-gray-600"># 启动系统</div>
                        <div class="text-blue-600">python run.py</div>
                        <br>
                        <div class="text-gray-600"># 访问地址</div>
                        <div class="text-green-600">http://localhost:8080</div>
                    </div>
                </div>
            </div>

            <div class="mt-8 text-center">
                <button onclick="openManual()" class="ios-button text-white px-8 py-3 rounded-xl text-lg">
                    <i class="fas fa-book mr-2"></i>查看详细部署文档
                </button>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="text-center py-8 text-gray-600">
        <p>&copy; 2024 综合管理系统. 基于韵通网络管理模式开发</p>
    </footer>

    <script>
        function openManual() {
            alert('请查看项目中的 docs/DEPLOYMENT.md 文件获取详细部署指南');
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为卡片添加悬停效果
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
