#!/bin/bash

echo "========================================"
echo "综合管理系统 - 前端演示启动器"
echo "========================================"
echo

echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "[错误] Python未安装"
    echo
    echo "解决方案:"
    echo "Ubuntu/Debian: sudo apt install python3"
    echo "CentOS/RHEL: sudo yum install python3"
    echo "macOS: brew install python3"
    echo
    echo "或者直接打开 frontend/index.html 查看静态演示"
    exit 1
fi

echo "Python环境检查通过"
echo

echo "正在启动前端演示服务器..."
echo "访问地址: http://localhost:8080"
echo "按 Ctrl+C 停止服务"
echo

cd frontend

# 尝试使用python3，如果不存在则使用python
if command -v python3 &> /dev/null; then
    python3 -m http.server 8080
else
    python -m http.server 8080
fi
