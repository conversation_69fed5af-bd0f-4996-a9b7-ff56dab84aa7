"""
统计分析服务
"""

from typing import List, Dict, Any
from datetime import date, datetime, timedelta
from collections import defaultdict
from backend.models.user import User
from backend.models.work_log import WorkLog
from backend.models.special_project import SpecialProject
from backend.services.work_log_service import WorkLogService

class StatsService:
    """统计分析服务类"""
    
    def __init__(self):
        self.work_log_service = WorkLogService()
    
    def get_recent_log_stats(self, days: int = 7) -> List[Dict[str, Any]]:
        """获取最近几天的日志统计"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        stats = []
        current_date = start_date
        
        while current_date <= end_date:
            log_count = WorkLog.count("log_date = ?", (current_date,))
            stats.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'count': log_count
            })
            current_date = date.fromordinal(current_date.toordinal() + 1)
        
        return stats
    
    def get_department_user_stats(self) -> List[Dict[str, Any]]:
        """获取部门用户统计"""
        users = User.find_all("is_active = 1")
        dept_stats = defaultdict(int)
        
        for user in users:
            department = user.department or '未分配部门'
            dept_stats[department] += 1
        
        return [
            {'department': dept, 'count': count}
            for dept, count in dept_stats.items()
        ]
    
    def get_user_completion_stats(self, days: int = 30) -> List[Dict[str, Any]]:
        """获取用户日志完成率统计"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        users = User.find_all("is_active = 1")
        stats = []
        
        for user in users:
            completion_rate = self.work_log_service.get_user_completion_rate(
                user.id, start_date, end_date
            )
            
            missing_logs = self.work_log_service.get_missing_logs(
                user.id, start_date, end_date
            )
            
            stats.append({
                'user_id': user.id,
                'username': user.username,
                'full_name': user.full_name,
                'department': user.department,
                'completion_rate': round(completion_rate, 2),
                'missing_days': len(missing_logs),
                'missing_dates': [d.strftime('%Y-%m-%d') for d in missing_logs[:5]]  # 只显示前5个
            })
        
        # 按完成率排序
        stats.sort(key=lambda x: x['completion_rate'], reverse=True)
        return stats
    
    def get_category_stats(self, start_date: date = None, end_date: date = None) -> List[Dict[str, Any]]:
        """获取日志分类统计"""
        if not start_date:
            start_date = date.today() - timedelta(days=30)
        if not end_date:
            end_date = date.today()
        
        logs = WorkLog.find_by_date_range(start_date, end_date)
        category_stats = defaultdict(int)
        
        for log in logs:
            category = log.category or '未分类'
            category_stats[category] += 1
        
        return [
            {'category': category, 'count': count}
            for category, count in category_stats.items()
        ]
    
    def get_monthly_project_stats(self, year: int = None) -> List[Dict[str, Any]]:
        """获取月度项目统计"""
        if not year:
            year = datetime.now().year
        
        stats = []
        
        for month in range(1, 13):
            month_str = f"{year}-{month:02d}"
            summary = SpecialProject.get_monthly_summary(month_str)
            
            stats.append({
                'month': month_str,
                'month_name': f"{year}年{month}月",
                **summary
            })
        
        return stats
    
    def get_operator_performance(self) -> List[Dict[str, Any]]:
        """获取运营商业绩统计"""
        return SpecialProject.get_operator_stats()
    
    def get_dashboard_summary(self) -> Dict[str, Any]:
        """获取仪表板汇总数据"""
        today = date.today()
        this_month = today.strftime("%Y-%m")
        
        # 基础统计
        total_users = User.count("is_active = 1")
        today_logs = WorkLog.count("log_date = ?", (today,))
        month_projects = SpecialProject.count("month = ?", (this_month,))
        
        # 本月项目汇总
        month_summary = SpecialProject.get_monthly_summary(this_month)
        
        # 最近7天日志趋势
        recent_logs = self.get_recent_log_stats(7)
        
        # 用户完成率概览
        completion_stats = self.get_user_completion_stats(7)
        avg_completion = sum(s['completion_rate'] for s in completion_stats) / len(completion_stats) if completion_stats else 0
        
        return {
            'basic_stats': {
                'total_users': total_users,
                'today_logs': today_logs,
                'month_projects': month_projects,
                'avg_completion_rate': round(avg_completion, 2)
            },
            'month_summary': month_summary,
            'recent_logs': recent_logs,
            'top_performers': completion_stats[:5],  # 前5名
            'low_performers': [s for s in completion_stats if s['completion_rate'] < 80]  # 完成率低于80%的用户
        }
    
    def get_workload_analysis(self, user_id: int = None, days: int = 30) -> Dict[str, Any]:
        """获取工作量分析"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        if user_id:
            logs = WorkLog.find_by_date_range(start_date, end_date, user_id)
            user = User.find_by_id(user_id)
            title = f"{user.full_name}的工作量分析" if user else "用户工作量分析"
        else:
            logs = WorkLog.find_by_date_range(start_date, end_date)
            title = "整体工作量分析"
        
        # 按日期统计
        daily_stats = defaultdict(int)
        for log in logs:
            daily_stats[log.log_date.strftime('%Y-%m-%d')] += 1
        
        # 按分类统计
        category_stats = defaultdict(int)
        for log in logs:
            category = log.category or '未分类'
            category_stats[category] += 1
        
        # 计算平均值
        total_logs = len(logs)
        avg_daily = total_logs / days if days > 0 else 0
        
        return {
            'title': title,
            'period': f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}",
            'total_logs': total_logs,
            'avg_daily': round(avg_daily, 2),
            'daily_stats': dict(daily_stats),
            'category_stats': dict(category_stats),
            'most_productive_day': max(daily_stats.items(), key=lambda x: x[1]) if daily_stats else None,
            'most_common_category': max(category_stats.items(), key=lambda x: x[1]) if category_stats else None
        }
